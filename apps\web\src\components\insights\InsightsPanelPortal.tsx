"use client";

import { useEffect, useState } from "react";
import { createPortal } from "react-dom";
import { InsightsPanel } from "./InsightsPanel";
import type { ComponentProps } from "react";

type InsightsPanelProps = ComponentProps<typeof InsightsPanel>;

export function InsightsPanelPortal(props: InsightsPanelProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  if (!mounted) {
    return null;
  }

  // Create a portal that renders the panel at the document body level
  // This ensures it's not affected by any transforms in parent elements
  return createPortal(
    <InsightsPanel {...props} />,
    document.body
  );
}