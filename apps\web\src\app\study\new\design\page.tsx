"use client";

import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { BlurFade } from "~/components/ui/blur-fade";
import { 
  FileText,
  Users,
  Target,
  Calendar,
  Activity,
  Building2,
  ChevronRight,
  Sparkles
} from "lucide-react";

export default function DesignPage() {
  const router = useRouter();
  const store = useTrialDesignStore();

  const designSections = [
    {
      id: "overview",
      title: "Study Overview",
      description: "Title, objectives, and rationale",
      icon: <FileText className="h-5 w-5" />,
      status: "pending" as const,
    },
    {
      id: "methodology",
      title: "Study Methodology",
      description: "Design, randomization, and blinding",
      icon: <Activity className="h-5 w-5" />,
      status: "pending" as const,
    },
    {
      id: "population",
      title: "Study Population",
      description: "Detailed inclusion/exclusion criteria",
      icon: <Users className="h-5 w-5" />,
      status: "pending" as const,
    },
    {
      id: "interventions",
      title: "Interventions",
      description: "Treatment arms and administration",
      icon: <Target className="h-5 w-5" />,
      status: "pending" as const,
    },
    {
      id: "endpoints",
      title: "Endpoints & Assessments",
      description: "Primary and secondary outcomes",
      icon: <Activity className="h-5 w-5" />,
      status: "pending" as const,
    },
    {
      id: "procedures",
      title: "Study Procedures",
      description: "Visit schedule and assessments",
      icon: <Calendar className="h-5 w-5" />,
      status: "pending" as const,
    },
    {
      id: "operational",
      title: "Operational Considerations",
      description: "Sites, timeline, and budget",
      icon: <Building2 className="h-5 w-5" />,
      status: "pending" as const,
    },
  ];

  const handleSectionClick = (sectionId: string) => {
    // In a full implementation, this would navigate to section-specific forms
    toast.info(`Design section "${sectionId}" coming soon!`);
  };

  const handleGenerateSynopsis = () => {
    store.markStepCompleted("design");
    store.setCurrentStep(3); // Move to synopsis step
    router.push("/study/new/synopsis");
  };

  return (
    <div className="space-y-8">
      <BlurFade delay={0.1} inView>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Design Your Trial
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Build your protocol based on AI-generated insights
          </p>
        </div>
      </BlurFade>

      <BlurFade delay={0.2} inView>
        <Card className="border-[#5A32FA]/20 bg-gradient-to-br from-purple-50 to-teal-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <Sparkles className="h-5 w-5 text-[#5A32FA]" />
              <p className="text-sm">
                AI insights have been applied to pre-populate recommendations. 
                Click each section to review and customize.
              </p>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {designSections.map((section, index) => (
          <BlurFade key={section.id} delay={0.3 + index * 0.05} inView>
            <Card 
              className="cursor-pointer transition-all hover:shadow-md hover:border-[#5A32FA]/30"
              onClick={() => handleSectionClick(section.id)}
            >
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-purple-100 text-[#5A32FA]">
                      {section.icon}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{section.title}</CardTitle>
                      <CardDescription className="mt-1">
                        {section.description}
                      </CardDescription>
                    </div>
                  </div>
                  <ChevronRight className="h-5 w-5 text-gray-400" />
                </div>
              </CardHeader>
            </Card>
          </BlurFade>
        ))}
      </div>

      <BlurFade delay={0.7} inView>
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={() => router.push("/study/new/insights")}
          >
            Back to Insights
          </Button>
          <Button 
            onClick={handleGenerateSynopsis}
            className="bg-gradient-to-r from-[#5A32FA] to-[#7D2AE8]"
          >
            Generate Synopsis
            <ChevronRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </BlurFade>
    </div>
  );
}

// Add missing import
import { toast } from "sonner";