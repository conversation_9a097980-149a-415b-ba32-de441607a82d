import { z } from "zod";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "~/server/api/trpc";
import { generateMockStudies, generateMockInsights } from "~/services/mock/trial-data";
import type { StudyDesignSession } from "~/types/trial-design";

// In-memory session storage for development
const sessions = new Map<string, StudyDesignSession>();

export const studyDesignRouter = createTRPCRouter({
  createSession: publicProcedure
    .input(z.object({
      userId: z.string().optional(),
    }).optional())
    .mutation(async ({ input, ctx }) => {
      const sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const session: StudyDesignSession = {
        id: sessionId,
        userId: input?.userId ?? ctx.session?.user?.id,
        status: "discovery",
        currentStep: "study-type",
        completedSteps: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        discovery: {
          studyType: "drug" as const,
          intervention: {},
          condition: "",
          population: {
            gender: "all",
          },
          objectives: {
            primaryGoal: "",
            keyOutcome: "",
          },
        },
      };
      
      sessions.set(sessionId, session);
      
      return {
        sessionId,
        expiresAt: session.expiresAt,
      };
    }),

  getSession: publicProcedure
    .input(z.object({
      sessionId: z.string(),
    }))
    .query(async ({ input }) => {
      const session = sessions.get(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      // Check if session is expired
      if (session.expiresAt < new Date()) {
        sessions.delete(input.sessionId);
        throw new Error("Session expired");
      }
      
      return session;
    }),

  saveDiscovery: publicProcedure
    .input(z.object({
      sessionId: z.string(),
      data: z.object({
        studyType: z.enum(["drug", "device", "behavioral", "diagnostic", "other"]).optional(),
        phase: z.enum(["phase1", "phase2", "phase3", "phase4", "unknown"]).optional(),
        intervention: z.object({
          name: z.string().optional(),
          category: z.string().optional(),
          mechanism: z.string().optional(),
          class: z.string().optional(),
          isNewCompound: z.boolean().optional(),
          deviceClass: z.string().optional(),
        }).optional(),
        condition: z.string().optional(),
        population: z.object({
          ageMin: z.number().optional(),
          ageMax: z.number().optional(),
          gender: z.enum(["all", "male", "female"]).optional(),
          specificPopulation: z.string().optional(),
          inclusionCriteria: z.array(z.string()).optional(),
          exclusionCriteria: z.array(z.string()).optional(),
        }).optional(),
        objectives: z.object({
          primaryGoal: z.string().optional(),
          keyOutcome: z.string().optional(),
          secondaryGoals: z.array(z.string()).optional(),
        }).optional(),
      }),
    }))
    .mutation(async ({ input }) => {
      const session = sessions.get(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      // Update discovery data - merge nested objects properly
      if (input.data.intervention) {
        session.discovery.intervention = {
          ...session.discovery.intervention,
          ...input.data.intervention,
        };
      }
      
      if (input.data.population) {
        session.discovery.population = {
          ...session.discovery.population,
          ...input.data.population,
        };
      }
      
      if (input.data.objectives) {
        session.discovery.objectives = {
          ...session.discovery.objectives,
          ...input.data.objectives,
        };
      }
      
      // Update top-level fields
      if (input.data.studyType) {
        session.discovery.studyType = input.data.studyType;
      }
      
      if (input.data.phase) {
        session.discovery.phase = input.data.phase;
      }
      
      if (input.data.condition) {
        session.discovery.condition = input.data.condition;
      }
      
      session.updatedAt = new Date();
      sessions.set(input.sessionId, session);
      
      return {
        success: true,
        sessionId: input.sessionId,
      };
    }),

  updateStep: publicProcedure
    .input(z.object({
      sessionId: z.string(),
      currentStep: z.string(),
      completedStep: z.string().optional(),
    }))
    .mutation(async ({ input }) => {
      const session = sessions.get(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      session.currentStep = input.currentStep;
      
      if (input.completedStep && !session.completedSteps.includes(input.completedStep)) {
        session.completedSteps.push(input.completedStep);
      }
      
      session.updatedAt = new Date();
      sessions.set(input.sessionId, session);
      
      return {
        success: true,
        currentStep: session.currentStep,
        completedSteps: session.completedSteps,
      };
    }),

  listSessions: protectedProcedure
    .query(async ({ ctx }) => {
      const userId = ctx.session?.user?.id;
      if (!userId) return [];
      
      const userSessions = Array.from(sessions.values())
        .filter(s => s.userId === userId && s.expiresAt > new Date())
        .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
      
      return userSessions;
    }),

  deleteSession: protectedProcedure
    .input(z.object({
      sessionId: z.string(),
    }))
    .mutation(async ({ input, ctx }) => {
      const session = sessions.get(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      // Check if user owns this session
      if (session.userId !== ctx.session?.user?.id) {
        throw new Error("Unauthorized");
      }
      
      sessions.delete(input.sessionId);
      
      return {
        success: true,
        deletedSessionId: input.sessionId,
      };
    }),

  searchKnowledgeBase: publicProcedure
    .input(z.object({
      sessionId: z.string(),
      discovery: z.any(), // Discovery data to search with
    }))
    .mutation(async ({ input }) => {
      const session = sessions.get(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      // In production, this would call AWS Lambda/Bedrock
      // For now, use mock data
      const studies = generateMockStudies(15);
      
      // Store results in session
      session.knowledgeBaseResults = {
        studies,
        selectedStudies: [],
        queryMetadata: {
          searchTime: Date.now(),
          totalResults: studies.length,
        },
      };
      session.status = "analyzing";
      session.updatedAt = new Date();
      
      sessions.set(input.sessionId, session);
      
      return {
        studies,
        metadata: session.knowledgeBaseResults.queryMetadata,
      };
    }),
  
  saveSelectedStudies: publicProcedure
    .input(z.object({
      sessionId: z.string(),
      studyIds: z.array(z.string()),
    }))
    .mutation(async ({ input }) => {
      const session = sessions.get(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      if (!session.knowledgeBaseResults) {
        throw new Error("No search results found");
      }
      
      // Update selected studies
      session.knowledgeBaseResults.selectedStudies = input.studyIds;
      
      // Generate insights based on selected studies
      // In production, this would call AWS Lambda/Bedrock
      const insights = generateMockInsights(input.studyIds);
      session.insights = insights;
      
      session.status = "designing";
      session.updatedAt = new Date();
      
      sessions.set(input.sessionId, session);
      
      return {
        success: true,
        insights,
      };
    }),
  
  getInsights: publicProcedure
    .input(z.object({
      sessionId: z.string(),
    }))
    .query(async ({ input }) => {
      const session = sessions.get(input.sessionId);
      
      if (!session) {
        throw new Error("Session not found");
      }
      
      if (!session.insights) {
        // Generate mock insights if not available
        const studyIds = session.knowledgeBaseResults?.selectedStudies || [];
        session.insights = generateMockInsights(studyIds);
        sessions.set(input.sessionId, session);
      }
      
      return session.insights;
    }),
});