"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { StudyTypeSelector } from "~/components/wizard/StudyTypeSelector";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { BlurFade } from "~/components/ui/blur-fade";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import type { StudyType } from "~/types/trial-design";

export default function StudyTypePage() {
  const router = useRouter();
  const store = useTrialDesignStore();
  const [selectedType, setSelectedType] = useState<StudyType | null>(
    store.discovery.studyType
  );

  // Create session mutation
  const createSession = api.studyDesign.createSession.useMutation({
    onSuccess: (data) => {
      store.setSessionId(data.sessionId);
      toast.success("Session created successfully");
    },
    onError: (error) => {
      toast.error("Failed to create session: " + error.message);
    },
  });

  // Save discovery mutation
  const saveDiscovery = api.studyDesign.saveDiscovery.useMutation({
    onSuccess: () => {
      store.markStepCompleted("study-type");
      router.push("/study/new/basics");
    },
    onError: (error) => {
      toast.error("Failed to save: " + error.message);
      // If session not found, create a new one
      if (error.message.includes("Session not found")) {
        store.resetSession();
        createSession.mutate({});
      }
    },
  });

  // Check session validity
  const { data: sessionData, error: sessionError } = api.studyDesign.getSession.useQuery(
    { sessionId: store.sessionId! },
    { 
      enabled: !!store.sessionId,
      retry: false,
    }
  );
  
  // Handle session error
  useEffect(() => {
    if (sessionError) {
      // Session expired or not found, create new one
      store.resetSession();
      createSession.mutate({});
    }
  }, [sessionError]);

  // Initialize session on mount
  useEffect(() => {
    if (!store.sessionId) {
      createSession.mutate({});
    }
  }, []);

  const handleContinue = async () => {
    if (!selectedType) {
      toast.error("Please select a study type");
      return;
    }

    if (!store.sessionId) {
      toast.error("Session not initialized");
      return;
    }

    // Update store
    store.updateDiscovery({ studyType: selectedType });

    // Save to backend
    saveDiscovery.mutate({
      sessionId: store.sessionId,
      data: {
        studyType: selectedType,
      },
    });
  };

  return (
    <div className="space-y-8">
      <BlurFade delay={0.1} inView>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            What type of study are you designing?
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Select the category that best describes your clinical trial
          </p>
        </div>
      </BlurFade>

      <BlurFade delay={0.2} inView>
        <StudyTypeSelector
          value={selectedType}
          onChange={setSelectedType}
        />
      </BlurFade>

      <BlurFade delay={0.3} inView>
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={() => router.push("/dashboard")}
          >
            Cancel
          </Button>
          <Button
            onClick={handleContinue}
            disabled={!selectedType || saveDiscovery.isPending}
          >
            {saveDiscovery.isPending ? "Saving..." : "Continue"}
          </Button>
        </div>
      </BlurFade>
    </div>
  );
}