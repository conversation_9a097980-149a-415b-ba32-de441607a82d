"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { Textarea } from "~/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { InsightsButton } from "~/components/insights/InsightsButton";
import { InsightsPanelPortal } from "~/components/insights/InsightsPanelPortal";
import { DocumentViewer } from "~/components/insights/DocumentViewer";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { ChevronLeft, Target, Plus, X, FlaskConical, Clock, Activity } from "lucide-react";
import type { StudyPhase } from "~/types/trial-design";

interface InsightSection {
  title: string;
  content: string;
  citations: Array<{
    id: string;
    title: string;
    url: string;
    relevance: number;
  }>;
  confidence?: number;
}

export default function StudyDesignPage() {
  const router = useRouter();
  const store = useTrialDesignStore();
  
  const [formData, setFormData] = useState({
    phase: store.discovery.phase || null as StudyPhase | null,
    primaryEndpoint: store.discovery.objectives?.primaryGoal || "",
    keyOutcomeMeasure: store.discovery.objectives?.keyOutcome || "",
    secondaryEndpoints: store.discovery.objectives?.secondaryGoals || [],
    studyDuration: store.discovery.objectives?.studyDuration || "",
    followUpPeriod: store.discovery.objectives?.followUpPeriod || "",
    // Design fields with defaults from store or fallbacks
    designType: store.discovery.design?.designType || "parallel" as "parallel" | "crossover" | "factorial" | "sequential",
    randomizationRatio: store.discovery.design?.randomizationRatio || "1:1",
    blinding: store.discovery.design?.blinding || "double-blind" as "open-label" | "single-blind" | "double-blind" | "triple-blind",
    controlType: store.discovery.design?.controlType || "placebo" as "placebo" | "active-comparator" | "standard-of-care" | "historical" | "none",
  });

  const [newSecondaryEndpoint, setNewSecondaryEndpoint] = useState("");
  const [activeInsightsPanel, setActiveInsightsPanel] = useState<string | null>(null);
  const [insightsData, setInsightsData] = useState<Record<string, { 
    sections: InsightSection[]; 
    sources?: any[];
    progressStatus?: string;
    progressMessages?: string[];
  }>>({});
  const [documentViewerUrl, setDocumentViewerUrl] = useState<string | null>(null);
  
  // Get cached insights from store
  const cachedInsights = store.insightsCache || {};

  // Query knowledge base for insights
  const queryInsights = api.knowledgeBase.queryInsights.useMutation({
    onSuccess: (data, variables) => {
      const insightsPayload = {
        sections: data.sections || [],
        sources: data.sources || []
      };
      
      // Update local state
      setInsightsData(prev => ({
        ...prev,
        [variables.field]: insightsPayload
      }));
      
      // Cache the insights in the store
      store.cacheInsights(variables.field, insightsPayload);
      
      setActiveInsightsPanel(variables.field);
    },
    onError: (error) => {
      toast.error("Failed to get insights: " + error.message);
    },
  });

  const saveDiscovery = api.studyDesign.saveDiscovery.useMutation({
    onSuccess: () => {
      store.markStepCompleted("study-design");
      router.push("/study/new/population");
    },
    onError: (error) => {
      toast.error("Failed to save: " + error.message);
    },
  });

  const getProgressUpdatesForField = (field: string) => {
    switch (field) {
      case "phase":
        return [
          { delay: 0, message: 'Initializing search...' },
          { delay: 1500, message: 'Searching knowledge base...' },
          { delay: 3500, message: 'Analyzing phase recommendations...' },
          { delay: 6000, message: 'Evaluating similar studies...' },
          { delay: 8500, message: 'Generating phase guidance...' },
        ];
      case "primary-endpoint":
        return [
          { delay: 0, message: 'Initializing search...' },
          { delay: 1500, message: 'Searching knowledge base...' },
          { delay: 3500, message: 'Analyzing endpoint strategies...' },
          { delay: 6000, message: 'Reviewing regulatory precedents...' },
          { delay: 8500, message: 'Generating endpoint recommendations...' },
        ];
      case "secondary-endpoints":
        return [
          { delay: 0, message: 'Initializing search...' },
          { delay: 1500, message: 'Searching knowledge base...' },
          { delay: 3500, message: 'Identifying secondary measures...' },
          { delay: 6000, message: 'Analyzing outcome hierarchies...' },
          { delay: 8500, message: 'Compiling endpoint options...' },
        ];
      case "study-duration":
        return [
          { delay: 0, message: 'Initializing search...' },
          { delay: 1500, message: 'Searching knowledge base...' },
          { delay: 3500, message: 'Analyzing timeline patterns...' },
          { delay: 6000, message: 'Calculating optimal durations...' },
          { delay: 8500, message: 'Generating timeline recommendations...' },
        ];
      case "design-parameters":
        return [
          { delay: 0, message: 'Initializing search...' },
          { delay: 1500, message: 'Searching knowledge base...' },
          { delay: 3500, message: 'Analyzing design patterns...' },
          { delay: 6000, message: 'Evaluating blinding strategies...' },
          { delay: 8500, message: 'Generating design recommendations...' },
        ];
      default:
        return [
          { delay: 0, message: 'Initializing search...' },
          { delay: 1500, message: 'Searching knowledge base...' },
          { delay: 3500, message: 'Analyzing relevant studies...' },
          { delay: 8500, message: 'Generating recommendations...' },
        ];
    }
  };

  const handleGetInsights = async (field: string, forceRefresh = false) => {
    // Check cache first unless forcing refresh
    if (!forceRefresh && cachedInsights[field]) {
      // Use cached data
      setInsightsData(prev => ({
        ...prev,
        [field]: cachedInsights[field]
      }));
      setActiveInsightsPanel(field);
      return;
    }
    
    // Show panel immediately with loading state
    setActiveInsightsPanel(field);
    
    // Set initial progress status
    setInsightsData(prev => ({
      ...prev,
      [field]: {
        sections: [],
        sources: [],
        progressStatus: 'Initializing search...',
        progressMessages: [],
      }
    }));
    
    // Progressive status updates based on field type
    const progressUpdates = getProgressUpdatesForField(field);
    
    // Update progress status progressively
    progressUpdates.forEach(({ delay, message }) => {
      setTimeout(() => {
        setInsightsData(prev => {
          const current = prev[field];
          // Only update if we're still loading (haven't received results yet)
          if (current && !current.sections?.length) {
            return {
              ...prev,
              [field]: {
                ...current,
                progressStatus: message,
                progressMessages: [...(current.progressMessages || []), message],
              }
            };
          }
          return prev;
        });
      }, delay);
    });
    
    const context = {
      studyType: store.discovery.studyType || undefined,
      condition: store.discovery.condition || undefined,
      drugClass: store.discovery.intervention.class || undefined,
      deviceClass: store.discovery.intervention.deviceClass || undefined,
      isNewCompound: store.discovery.intervention.isNewCompound || undefined,
      mechanism: store.discovery.intervention.mechanism || undefined,
      phase: formData.phase || undefined,
      // Include design parameters when requesting primary endpoint
      ...(field === 'primary-endpoint' ? {
        designType: formData.designType || undefined,
        blinding: formData.blinding || undefined,
        controlType: formData.controlType || undefined,
        randomizationRatio: formData.randomizationRatio || undefined,
      } : {}),
      // Include primary endpoint when requesting secondary endpoints or design parameters
      ...(field === 'secondary-endpoints' || field === 'design-parameters' ? {
        primaryEndpoint: formData.primaryEndpoint && formData.primaryEndpoint.trim() !== '' 
          ? formData.primaryEndpoint.trim() 
          : 'Not yet determined'
      } : {})
    };

    const query = getQueryForField(field);

    // Log the query details to browser console
    console.log("=== INSIGHTS QUERY TO KNOWLEDGE BASE ===");
    console.log("Field:", field);
    console.log("Query:", query);
    console.log("Context:", context);
    console.log("Session ID:", store.sessionId);
    console.log("Using cache:", !forceRefresh && !!cachedInsights[field]);
    console.log("==========================================");

    await queryInsights.mutateAsync({
      sessionId: store.sessionId!,
      field,
      context,
      query,
    });
  };

  const getQueryForField = (field: string) => {
    switch (field) {
      case "phase":
        return `What phase should I choose for a ${store.discovery.intervention.isNewCompound ? 'novel' : 'existing'} ${store.discovery.intervention.class || 'drug'} targeting ${store.discovery.condition}?`;
      case "primary-endpoint":
        return `What are appropriate primary endpoints for a ${store.discovery.studyType} study in ${store.discovery.condition}?`;
      case "secondary-endpoints":
        return `What secondary endpoints are commonly used in ${store.discovery.condition} trials?`;
      case "study-duration":
        return `What is the typical study duration and follow-up period for ${store.discovery.condition} trials?`;
      case "design-parameters":
        return `What study design parameters (design type, blinding, control type, randomization) are most appropriate for a ${formData.phase || 'Phase 2'} ${store.discovery.studyType} trial in ${store.discovery.condition}?`;
      default:
        return "";
    }
  };

  const handleApplySuggestion = (field: string, suggestion: string, actionableData?: any) => {
    // Parse and apply the suggestion based on the field
    if (field === "phase") {
      // Extract phase from suggestion text
      const phaseMatch = suggestion.match(/phase\s*([1-4]|I{1,3}|IV)/i);
      if (phaseMatch) {
        const phaseMap: Record<string, StudyPhase> = {
          "1": "phase1", "I": "phase1",
          "2": "phase2", "II": "phase2", 
          "3": "phase3", "III": "phase3",
          "4": "phase4", "IV": "phase4",
        };
        const matchKey = phaseMatch[1];
        if (matchKey) {
          const phase = phaseMap[matchKey.toUpperCase()];
          if (phase) {
            setFormData(prev => ({ ...prev, phase }));
            toast.success("Phase recommendation applied");
          }
        }
      }
    } else if (field === "primary-endpoint") {
      // Use actionableData if available (from JSON response)
      if (actionableData && actionableData.field === 'primaryEndpoint') {
        setFormData(prev => ({ 
          ...prev, 
          primaryEndpoint: actionableData.value,
          keyOutcomeMeasure: actionableData.measurementMethod || prev.keyOutcomeMeasure
        }));
        toast.success("Primary endpoint applied");
      } else {
        // Fallback to setting suggestion directly
        setFormData(prev => ({ ...prev, primaryEndpoint: suggestion }));
        toast.success("Primary endpoint applied");
      }
    } else if (field === "secondary-endpoints") {
      // Keep the full endpoint text with description, just format it nicely
      const endpointText = suggestion.replace(/:\s+/, ' - ').trim();
      
      // Check if this endpoint is already in the list (check both exact match and title match)
      const endpointTitle = endpointText.split(' - ')[0];
      const isDuplicate = formData.secondaryEndpoints.some(ep => 
        ep === endpointText || ep.startsWith(endpointTitle + ' - ')
      );
      
      if (isDuplicate) {
        toast.info("This endpoint is already in your list");
        return; // Don't close panel
      }
      
      // Add to the array of secondary endpoints
      setFormData(prev => ({
        ...prev,
        secondaryEndpoints: [...prev.secondaryEndpoints, endpointText]
      }));
      toast.success("Secondary endpoint added");
      
      // Don't close the panel so user can add more endpoints
      return;
    } else if (field === "study-duration") {
      // Check if the suggestion contains "Recommended:" which indicates structured data
      if (suggestion.includes("Recommended:")) {
        // Parse structured recommendation
        const recommendedMatch = suggestion.match(/Recommended:\s*([^\n]+)/);
        if (recommendedMatch && recommendedMatch[1]) {
          const value = recommendedMatch[1].trim();
          
          // Check if this is for follow-up period or treatment duration
          if (suggestion.toLowerCase().includes("follow") || suggestion.toLowerCase().includes("follow-up")) {
            setFormData(prev => ({ ...prev, followUpPeriod: value }));
            toast.success("Follow-up period applied");
          } else {
            setFormData(prev => ({ ...prev, studyDuration: value }));
            toast.success("Study duration applied");
          }
        }
      } else {
        // Fallback: Extract duration from plain text suggestion
        const durationMatch = suggestion.match(/(\d+)\s*(weeks?|months?|years?)/i);
        if (durationMatch) {
          setFormData(prev => ({ ...prev, studyDuration: `${durationMatch[1]} ${durationMatch[2]}` }));
          toast.success("Study duration applied");
        }
      }
      // Don't close the panel so user can apply both duration fields
      return;
    } else if (field === "design-parameters") {
      // Use actionableData if available (from JSON response)
      if (actionableData) {
        const updates: any = {};
        
        if (actionableData.field === 'designType' && actionableData.value) {
          updates.designType = actionableData.value;
        } else if (actionableData.field === 'blinding' && actionableData.value) {
          updates.blinding = actionableData.value;
        } else if (actionableData.field === 'controlType' && actionableData.value) {
          updates.controlType = actionableData.value;
        } else if (actionableData.field === 'randomizationRatio' && actionableData.value) {
          updates.randomizationRatio = actionableData.value;
        }
        
        if (Object.keys(updates).length > 0) {
          setFormData(prev => ({ ...prev, ...updates }));
          toast.success(`${actionableData.field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())} updated`);
        }
      } else {
        // Fallback to regex parsing if no actionableData
        const designTypeMatch = suggestion.match(/(?:parallel|crossover|factorial|adaptive|platform|basket|umbrella|sequential|cluster|stepped-wedge|n-of-1|single-arm)/i);
        const blindingMatch = suggestion.match(/(?:open-label|single-blind|double-blind|triple-blind|quadruple-blind)/i);
        const controlMatch = suggestion.match(/(?:placebo|active-comparator|standard-of-care|dose-comparison|sham|historical|waitlist|usual-care|no-treatment)/i);
        const ratioMatch = suggestion.match(/(\d+):(\d+)(?::(\d+))?/);
        
        if (designTypeMatch) {
          const designType = designTypeMatch[0].toLowerCase().replace(/-/g, '-');
          setFormData(prev => ({ ...prev, designType: designType as any }));
        }
        if (blindingMatch) {
          const blinding = blindingMatch[0].toLowerCase();
          setFormData(prev => ({ ...prev, blinding: blinding as any }));
        }
        if (controlMatch) {
          const controlType = controlMatch[0].toLowerCase().replace(/ /g, '-');
          setFormData(prev => ({ ...prev, controlType: controlType as any }));
        }
        if (ratioMatch) {
          setFormData(prev => ({ ...prev, randomizationRatio: ratioMatch[0] }));
        }
        
        toast.success("Design parameters updated");
      }
      // Don't close panel so user can review all recommendations
      return;
    } else {
      setActiveInsightsPanel(null);
    }
  };

  const handleContinue = () => {
    if (!formData.phase) {
      toast.error("Please select a study phase");
      return;
    }

    if (!formData.primaryEndpoint) {
      toast.error("Please provide a primary endpoint");
      return;
    }

    if (!store.sessionId) {
      toast.error("Session not found");
      return;
    }

    // Update store
    store.updateDiscovery({ 
      phase: formData.phase,
      objectives: {
        primaryGoal: formData.primaryEndpoint,
        keyOutcome: formData.keyOutcomeMeasure,
        secondaryGoals: formData.secondaryEndpoints,
        studyDuration: formData.studyDuration,
        followUpPeriod: formData.followUpPeriod,
      },
      design: {
        designType: formData.designType,
        randomizationRatio: formData.randomizationRatio,
        blinding: formData.blinding,
        controlType: formData.controlType,
      }
    });

    // Save to backend
    saveDiscovery.mutate({
      sessionId: store.sessionId,
      data: {
        phase: formData.phase,
        objectives: {
          primaryGoal: formData.primaryEndpoint,
          keyOutcome: formData.keyOutcomeMeasure,
          secondaryGoals: formData.secondaryEndpoints,
          studyDuration: formData.studyDuration,
          followUpPeriod: formData.followUpPeriod,
        },
        design: {
          designType: formData.designType,
          randomizationRatio: formData.randomizationRatio,
          blinding: formData.blinding,
          controlType: formData.controlType,
        }
      },
    });
  };

  const addSecondaryEndpoint = () => {
    if (newSecondaryEndpoint.trim()) {
      setFormData({
        ...formData,
        secondaryEndpoints: [...formData.secondaryEndpoints, newSecondaryEndpoint.trim()],
      });
      setNewSecondaryEndpoint("");
    }
  };

  const removeSecondaryEndpoint = (index: number) => {
    setFormData({
      ...formData,
      secondaryEndpoints: formData.secondaryEndpoints.filter((_, i) => i !== index),
    });
  };

  return (
    <div className="space-y-8">
      <BlurFade delay={0.1} inView>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Study Design
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Let's design your study with insights from similar trials
          </p>
        </div>
      </BlurFade>

      <BlurFade delay={0.2} inView>
        <Card className="border-[#5A32FA]/20 bg-gradient-to-br from-purple-50 to-teal-50">
          <CardHeader>
            <CardTitle className="text-sm font-medium text-gray-600">Study Context</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-700">
              Designing a {store.discovery.studyType} study for {store.discovery.condition || "the specified condition"} 
              {store.discovery.intervention.class && ` using a ${store.discovery.intervention.class}`}
            </p>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.3} inView>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FlaskConical className="h-5 w-5" />
              Study Phase
            </CardTitle>
            <CardDescription>
              Select the appropriate phase for your trial
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <RadioGroup
                value={formData.phase || ""}
                onValueChange={(value) => setFormData({ ...formData, phase: value as StudyPhase })}
                className="flex-1"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="phase1" id="phase1" />
                  <Label htmlFor="phase1" className="font-normal">
                    Phase 1 - Safety, dosing, pharmacokinetics
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="phase2" id="phase2" />
                  <Label htmlFor="phase2" className="font-normal">
                    Phase 2 - Efficacy and side effects
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="phase3" id="phase3" />
                  <Label htmlFor="phase3" className="font-normal">
                    Phase 3 - Efficacy comparison and monitoring
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="phase4" id="phase4" />
                  <Label htmlFor="phase4" className="font-normal">
                    Phase 4 - Post-market surveillance
                  </Label>
                </div>
              </RadioGroup>
              <InsightsButton
                onClick={() => handleGetInsights("phase")}
                onRefresh={() => handleGetInsights("phase", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "phase"}
                hasCachedData={!!cachedInsights["phase"]}
                showRefresh={!!cachedInsights["phase"]}
                className="ml-4"
              />
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.35} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Study Design Parameters
                </CardTitle>
                <CardDescription>
                  Design structure, randomization, and blinding
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("design-parameters")}
                onRefresh={() => handleGetInsights("design-parameters", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "design-parameters"}
                hasCachedData={!!cachedInsights["design-parameters"]}
                showRefresh={!!cachedInsights["design-parameters"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="designType">Study Design Type</Label>
                <Select
                  value={formData.designType}
                  onValueChange={(value) => setFormData({ ...formData, designType: value as any })}
                >
                  <SelectTrigger id="designType">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="parallel">Parallel Group</SelectItem>
                    <SelectItem value="crossover">Crossover</SelectItem>
                    <SelectItem value="factorial">Factorial</SelectItem>
                    <SelectItem value="sequential">Sequential</SelectItem>
                    <SelectItem value="adaptive">Adaptive</SelectItem>
                    <SelectItem value="cluster">Cluster Randomized</SelectItem>
                    <SelectItem value="stepped-wedge">Stepped Wedge</SelectItem>
                    <SelectItem value="n-of-1">N-of-1</SelectItem>
                    <SelectItem value="single-arm">Single Arm</SelectItem>
                    <SelectItem value="basket">Basket Trial</SelectItem>
                    <SelectItem value="umbrella">Umbrella Trial</SelectItem>
                    <SelectItem value="platform">Platform Trial</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="randomizationRatio">Randomization Ratio</Label>
                <Input
                  id="randomizationRatio"
                  placeholder="e.g., 1:1, 2:1, 1:1:1"
                  value={formData.randomizationRatio}
                  onChange={(e) => setFormData({ ...formData, randomizationRatio: e.target.value })}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="blinding">Blinding</Label>
                <Select
                  value={formData.blinding}
                  onValueChange={(value) => setFormData({ ...formData, blinding: value as any })}
                >
                  <SelectTrigger id="blinding">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="open-label">Open Label (No Blinding)</SelectItem>
                    <SelectItem value="single-blind">Single Blind (Participant)</SelectItem>
                    <SelectItem value="single-blind-investigator">Single Blind (Investigator)</SelectItem>
                    <SelectItem value="single-blind-assessor">Single Blind (Assessor)</SelectItem>
                    <SelectItem value="double-blind">Double Blind (Participant + Investigator)</SelectItem>
                    <SelectItem value="triple-blind">Triple Blind (Participant + Investigator + Assessor)</SelectItem>
                    <SelectItem value="quadruple-blind">Quadruple Blind (All Parties)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="controlType">Control Type</Label>
                <Select
                  value={formData.controlType}
                  onValueChange={(value) => setFormData({ ...formData, controlType: value as any })}
                >
                  <SelectTrigger id="controlType">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="placebo">Placebo Control</SelectItem>
                    <SelectItem value="active-comparator">Active Comparator</SelectItem>
                    <SelectItem value="standard-of-care">Standard of Care</SelectItem>
                    <SelectItem value="dose-comparison">Dose Comparison</SelectItem>
                    <SelectItem value="sham">Sham Procedure</SelectItem>
                    <SelectItem value="historical">Historical Control</SelectItem>
                    <SelectItem value="waitlist">Waitlist Control</SelectItem>
                    <SelectItem value="usual-care">Usual Care</SelectItem>
                    <SelectItem value="no-treatment">No Treatment</SelectItem>
                    <SelectItem value="none">No Control (Single Arm)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.4} inView>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Primary Endpoint
            </CardTitle>
            <CardDescription>
              The main outcome measure for your study
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="primaryEndpoint">Primary Endpoint *</Label>
                <InsightsButton
                  onClick={() => handleGetInsights("primary-endpoint")}
                  onRefresh={() => handleGetInsights("primary-endpoint", true)}
                  loading={queryInsights.isPending && queryInsights.variables?.field === "primary-endpoint"}
                  hasCachedData={!!cachedInsights["primary-endpoint"]}
                  showRefresh={!!cachedInsights["primary-endpoint"]}
                />
              </div>
              <Textarea
                id="primaryEndpoint"
                placeholder="e.g., Change in HbA1c from baseline at 12 weeks"
                value={formData.primaryEndpoint}
                onChange={(e) => setFormData({ ...formData, primaryEndpoint: e.target.value })}
                rows={2}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="keyOutcomeMeasure">Measurement Details</Label>
              <Input
                id="keyOutcomeMeasure"
                placeholder="e.g., Percentage point change, measured via laboratory test"
                value={formData.keyOutcomeMeasure}
                onChange={(e) => setFormData({ ...formData, keyOutcomeMeasure: e.target.value })}
              />
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.5} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Secondary Endpoints</CardTitle>
                <CardDescription>
                  Additional outcomes to measure (optional)
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("secondary-endpoints")}
                onRefresh={() => handleGetInsights("secondary-endpoints", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "secondary-endpoints"}
                hasCachedData={!!cachedInsights["secondary-endpoints"]}
                showRefresh={!!cachedInsights["secondary-endpoints"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {formData.secondaryEndpoints.map((endpoint, index) => (
              <div key={index} className="flex items-center gap-2 rounded-lg border p-3">
                <span className="flex-1 text-sm">{endpoint}</span>
                <Button
                  size="icon"
                  variant="ghost"
                  onClick={() => removeSecondaryEndpoint(index)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
            
            <div className="flex gap-2">
              <Input
                placeholder="Add secondary endpoint..."
                value={newSecondaryEndpoint}
                onChange={(e) => setNewSecondaryEndpoint(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addSecondaryEndpoint())}
              />
              <Button onClick={addSecondaryEndpoint} size="icon">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.6} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Study Timeline
                </CardTitle>
                <CardDescription>
                  Expected duration and follow-up period
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("study-duration")}
                onRefresh={() => handleGetInsights("study-duration", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "study-duration"}
                hasCachedData={!!cachedInsights["study-duration"]}
                showRefresh={!!cachedInsights["study-duration"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="studyDuration">Study Duration</Label>
                <Input
                  id="studyDuration"
                  placeholder="e.g., 12 weeks, 6 months"
                  value={formData.studyDuration}
                  onChange={(e) => setFormData({ ...formData, studyDuration: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="followUpPeriod">Follow-up Period</Label>
                <Input
                  id="followUpPeriod"
                  placeholder="e.g., 4 weeks, 3 months"
                  value={formData.followUpPeriod}
                  onChange={(e) => setFormData({ ...formData, followUpPeriod: e.target.value })}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.7} inView>
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={() => router.push("/study/new/basics")}
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <Button
            onClick={handleContinue}
            disabled={!formData.phase || !formData.primaryEndpoint || saveDiscovery.isPending}
          >
            {saveDiscovery.isPending ? "Saving..." : "Continue to Population"}
          </Button>
        </div>
      </BlurFade>

      {/* Insights Panel */}
      {activeInsightsPanel && (
        <InsightsPanelPortal
          isOpen={true}
          onClose={() => setActiveInsightsPanel(null)}
          title={`Insights: ${activeInsightsPanel.replace("-", " ").replace(/\b\w/g, l => l.toUpperCase())}`}
          description="Recommendations based on similar studies in our knowledge base"
          loading={queryInsights.isPending}
          progressStatus={insightsData[activeInsightsPanel]?.progressStatus}
          progressMessages={insightsData[activeInsightsPanel]?.progressMessages || []}
          sections={insightsData[activeInsightsPanel]?.sections || cachedInsights[activeInsightsPanel]?.sections || []}
          sources={insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources || []}
          onDocumentClick={(url) => setDocumentViewerUrl(url)}
          onApplySuggestion={(suggestion, actionableData) => handleApplySuggestion(activeInsightsPanel, suggestion, actionableData)}
        />
      )}

      {/* Document Viewer */}
      {documentViewerUrl && (
        <DocumentViewer
          isOpen={true}
          onClose={() => setDocumentViewerUrl(null)}
          documentUrl={documentViewerUrl}
          loading={false}
        />
      )}
    </div>
  );
}