"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { RadioGroup, RadioGroupItem } from "~/components/ui/radio-group";
import { Textarea } from "~/components/ui/textarea";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { ChevronLeft, Pill, Stethoscope, Brain, TestTube, HelpCircle } from "lucide-react";

export default function InterventionPage() {
  const router = useRouter();
  const store = useTrialDesignStore();
  const studyType = store.discovery.studyType;
  
  const [formData, setFormData] = useState({
    name: store.discovery.intervention.name || "",
    category: store.discovery.intervention.category || "",
    mechanism: store.discovery.intervention.mechanism || "",
    class: store.discovery.intervention.class || "",
    isNewCompound: store.discovery.intervention.isNewCompound || false,
    deviceClass: store.discovery.intervention.deviceClass || "",
  });

  const saveDiscovery = api.studyDesign.saveDiscovery.useMutation({
    onSuccess: () => {
      store.markStepCompleted("intervention");
      router.push("/study/new/population");
    },
    onError: (error) => {
      toast.error("Failed to save: " + error.message);
    },
  });

  const handleContinue = () => {
    if (!formData.name) {
      toast.error("Please provide an intervention name");
      return;
    }

    if (!store.sessionId) {
      toast.error("Session not found");
      return;
    }

    // Update store
    store.updateDiscovery({ intervention: formData });

    // Save to backend
    saveDiscovery.mutate({
      sessionId: store.sessionId,
      data: {
        intervention: formData,
      },
    });
  };

  const interventionIcons = {
    drug: <Pill className="h-5 w-5" />,
    device: <Stethoscope className="h-5 w-5" />,
    behavioral: <Brain className="h-5 w-5" />,
    diagnostic: <TestTube className="h-5 w-5" />,
    other: <HelpCircle className="h-5 w-5" />,
  };

  return (
    <div className="space-y-8">
      <BlurFade delay={0.1} inView>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Describe your intervention
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Provide details about your {studyType || "study"} intervention
          </p>
        </div>
      </BlurFade>

      <BlurFade delay={0.2} inView>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {interventionIcons[studyType as keyof typeof interventionIcons] || interventionIcons.other}
              Basic Information
            </CardTitle>
            <CardDescription>
              Core details about your intervention
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="name">Intervention Name *</Label>
              <Input
                id="name"
                placeholder={studyType === "drug" ? "e.g., Investigational Drug XYZ" : "e.g., Device/Treatment Name"}
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              />
            </div>

            {studyType === "drug" && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="class">Drug Class</Label>
                  <Input
                    id="class"
                    placeholder="e.g., ACE inhibitor, SGLT-2 inhibitor"
                    value={formData.class}
                    onChange={(e) => setFormData({ ...formData, class: e.target.value })}
                  />
                </div>

                <div className="space-y-3">
                  <Label>Is this a new compound?</Label>
                  <RadioGroup
                    value={formData.isNewCompound ? "yes" : "no"}
                    onValueChange={(value) => setFormData({ ...formData, isNewCompound: value === "yes" })}
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="yes" id="new-yes" />
                      <Label htmlFor="new-yes" className="font-normal">
                        Yes, this is a novel compound
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="no" id="new-no" />
                      <Label htmlFor="new-no" className="font-normal">
                        No, this is an existing compound
                      </Label>
                    </div>
                  </RadioGroup>
                </div>
              </>
            )}

            {studyType === "device" && (
              <div className="space-y-2">
                <Label htmlFor="deviceClass">Device Classification</Label>
                <RadioGroup
                  value={formData.deviceClass}
                  onValueChange={(value) => setFormData({ ...formData, deviceClass: value })}
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="class-i" id="class-i" />
                    <Label htmlFor="class-i" className="font-normal">
                      Class I (Low Risk)
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="class-ii" id="class-ii" />
                    <Label htmlFor="class-ii" className="font-normal">
                      Class II (Moderate Risk)
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="class-iii" id="class-iii" />
                    <Label htmlFor="class-iii" className="font-normal">
                      Class III (High Risk)
                    </Label>
                  </div>
                </RadioGroup>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="category">Category/Type</Label>
              <Input
                id="category"
                placeholder={
                  studyType === "drug" 
                    ? "e.g., Oral, Injectable, Topical" 
                    : studyType === "device"
                    ? "e.g., Implantable, Wearable, Diagnostic"
                    : "e.g., Cognitive therapy, Exercise program"
                }
                value={formData.category}
                onChange={(e) => setFormData({ ...formData, category: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="mechanism">
                {studyType === "behavioral" ? "Approach/Method" : "Mechanism of Action"}
              </Label>
              <Textarea
                id="mechanism"
                placeholder={
                  studyType === "drug"
                    ? "Briefly describe how the drug works..."
                    : studyType === "device"
                    ? "Briefly describe how the device functions..."
                    : "Briefly describe the intervention approach..."
                }
                value={formData.mechanism}
                onChange={(e) => setFormData({ ...formData, mechanism: e.target.value })}
                rows={3}
              />
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.3} inView>
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={() => router.push("/study/new")}
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <Button
            onClick={handleContinue}
            disabled={!formData.name || saveDiscovery.isPending}
          >
            {saveDiscovery.isPending ? "Saving..." : "Continue"}
          </Button>
        </div>
      </BlurFade>
    </div>
  );
}