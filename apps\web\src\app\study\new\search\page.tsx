"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Checkbox } from "~/components/ui/checkbox";
import { BlurFade } from "~/components/ui/blur-fade";
import { Skeleton } from "~/components/ui/skeleton";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { Search, CheckCircle2, AlertCircle, Calendar, Users, TrendingUp, ChevronRight } from "lucide-react";
import { generateMockStudies } from "~/services/mock/trial-data";
import type { StudyResult } from "~/types/trial-design";
import { ShimmerButton } from "~/components/ui/shimmer-button";

export default function SearchPage() {
  const router = useRouter();
  const store = useTrialDesignStore();
  const [studies, setStudies] = useState<StudyResult[]>([]);
  const [selectedStudies, setSelectedStudies] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(true);

  const searchStudies = api.studyDesign.searchKnowledgeBase.useMutation({
    onSuccess: (data) => {
      setStudies(data.studies);
      setIsLoading(false);
      toast.success(`Found ${data.studies.length} relevant studies`);
    },
    onError: (error) => {
      // Fallback to mock data on error
      const mockData = generateMockStudies(15);
      setStudies(mockData);
      setIsLoading(false);
      toast.info("Using sample data for demonstration");
    },
  });

  const saveSelectedStudies = api.studyDesign.saveSelectedStudies.useMutation({
    onSuccess: () => {
      store.markStepCompleted("search");
      router.push("/study/new/insights");
    },
    onError: (error) => {
      toast.error("Failed to save selection: " + error.message);
    },
  });

  useEffect(() => {
    if (store.sessionId && store.discovery) {
      // Perform search based on discovery data
      searchStudies.mutate({
        sessionId: store.sessionId,
        discovery: store.discovery,
      });
    }
  }, []);

  const toggleStudySelection = (studyId: string) => {
    const newSelection = new Set(selectedStudies);
    if (newSelection.has(studyId)) {
      newSelection.delete(studyId);
    } else {
      newSelection.add(studyId);
    }
    setSelectedStudies(newSelection);
  };

  const handleContinue = () => {
    if (selectedStudies.size === 0) {
      toast.error("Please select at least one study to analyze");
      return;
    }

    if (!store.sessionId) {
      toast.error("Session not found");
      return;
    }

    saveSelectedStudies.mutate({
      sessionId: store.sessionId,
      studyIds: Array.from(selectedStudies),
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "active":
      case "recruiting":
        return "bg-blue-100 text-blue-800";
      case "terminated":
      case "withdrawn":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPhaseColor = (phase?: string) => {
    switch (phase) {
      case "phase1":
        return "bg-orange-100 text-orange-800";
      case "phase2":
        return "bg-amber-100 text-amber-800";
      case "phase3":
        return "bg-purple-100 text-purple-800";
      case "phase4":
        return "bg-indigo-100 text-indigo-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-8">
      <BlurFade delay={0.1} inView>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Similar Studies Found
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Select the most relevant studies to inform your trial design
          </p>
        </div>
      </BlurFade>

      <BlurFade delay={0.2} inView>
        <Card className="border-[#00C4CC]/20 bg-gradient-to-br from-teal-50 to-purple-50">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Search className="h-5 w-5 text-[#5A32FA]" />
                <span className="text-sm font-medium">
                  {isLoading ? "Searching..." : `${studies.length} studies found`}
                </span>
              </div>
              {!isLoading && (
                <Badge variant="secondary">
                  {selectedStudies.size} selected
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      <div className="space-y-4">
        {isLoading ? (
          // Loading skeletons
          Array.from({ length: 3 }).map((_, i) => (
            <BlurFade key={i} delay={0.3 + i * 0.1} inView>
              <Card>
                <CardHeader>
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-20 w-full" />
                </CardContent>
              </Card>
            </BlurFade>
          ))
        ) : (
          studies.map((study, index) => (
            <BlurFade key={study.id} delay={0.3 + Math.min(index * 0.05, 0.5)} inView>
              <Card 
                className={`transition-all ${
                  selectedStudies.has(study.id) 
                    ? "border-[#5A32FA] shadow-md" 
                    : "hover:shadow-sm"
                }`}
              >
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3">
                        <Checkbox
                          checked={selectedStudies.has(study.id)}
                          onCheckedChange={() => toggleStudySelection(study.id)}
                        />
                        <CardTitle className="text-lg">{study.title}</CardTitle>
                      </div>
                      <div className="mt-2 flex flex-wrap gap-2">
                        <Badge className={getStatusColor(study.status)}>
                          {study.status}
                        </Badge>
                        {study.phase && (
                          <Badge className={getPhaseColor(study.phase)}>
                            {study.phase.replace("phase", "Phase ")}
                          </Badge>
                        )}
                        <Badge variant="outline">
                          {study.nctId}
                        </Badge>
                        <Badge variant="secondary" className="flex items-center gap-1">
                          <TrendingUp className="h-3 w-3" />
                          {Math.round(study.relevanceScore * 100)}% match
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <CardDescription className="mt-3">
                    {study.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-gray-400" />
                      <span>{study.enrollment} enrolled</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span>{new Date(study.startDate).getFullYear()}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {study.results ? (
                        <>
                          <CheckCircle2 className="h-4 w-4 text-green-500" />
                          <span>Has results</span>
                        </>
                      ) : (
                        <>
                          <AlertCircle className="h-4 w-4 text-amber-500" />
                          <span>No results yet</span>
                        </>
                      )}
                    </div>
                  </div>
                  {study.highlights.length > 0 && (
                    <div className="mt-4 space-y-1">
                      <p className="text-xs font-medium text-gray-500">Key Matches:</p>
                      <div className="flex flex-wrap gap-1">
                        {study.highlights.map((highlight, i) => (
                          <Badge key={i} variant="secondary" className="text-xs">
                            {highlight}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </BlurFade>
          ))
        )}
      </div>

      <BlurFade delay={0.8} inView>
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={() => router.push("/study/new/objectives")}
          >
            Back to Objectives
          </Button>
          <ShimmerButton
            onClick={handleContinue}
            disabled={selectedStudies.size === 0 || saveSelectedStudies.isPending}
          >
            {saveSelectedStudies.isPending ? (
              "Generating Insights..."
            ) : (
              <>
                Generate Insights
                <ChevronRight className="ml-2 h-4 w-4" />
              </>
            )}
          </ShimmerButton>
        </div>
      </BlurFade>
    </div>
  );
}