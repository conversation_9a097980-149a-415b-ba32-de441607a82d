"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { InsightsButton } from "~/components/insights/InsightsButton";
import { InsightsPanel } from "~/components/insights/InsightsPanel";
import { DocumentViewer } from "~/components/insights/DocumentViewer";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { 
  <PERSON><PERSON>ronLeft, 
  Building2, 
  <PERSON>, 
  TrendingUp,
  Database,
  Monitor,
  Globe,
  Plus,
  X
} from "lucide-react";

export default function OperationalPage() {
  const router = useRouter();
  const store = useTrialDesignStore();
  
  const [formData, setFormData] = useState({
    numberOfSites: store.discovery.operational?.numberOfSites || "5",
    sitesPerCountry: store.discovery.operational?.sitesPerCountry || {} as Record<string, number>,
    recruitmentRate: store.discovery.operational?.recruitmentRate || "2-3 patients/site/month",
    screenFailureRate: store.discovery.operational?.screenFailureRate || "20%",
    dropoutRate: store.discovery.operational?.dropoutRate || "15%",
    dataManagement: store.discovery.operational?.dataManagement || "edc" as "edc" | "paper" | "hybrid",
    monitoringApproach: store.discovery.operational?.monitoringApproach || "risk-based" as "on-site" | "remote" | "risk-based" | "hybrid",
  });

  const [newCountry, setNewCountry] = useState("");
  const [newSiteCount, setNewSiteCount] = useState("");
  const [activeInsightsPanel, setActiveInsightsPanel] = useState<string | null>(null);
  const [insightsData, setInsightsData] = useState<Record<string, any>>({});
  const [documentViewerUrl, setDocumentViewerUrl] = useState<string | null>(null);
  
  const cachedInsights = store.insightsCache || {};

  const queryInsights = api.knowledgeBase.queryInsights.useMutation({
    onSuccess: (data, variables) => {
      const insightsPayload = {
        sections: data.sections || [],
        sources: data.sources || []
      };
      
      setInsightsData(prev => ({
        ...prev,
        [variables.field]: insightsPayload
      }));
      
      store.cacheInsights(variables.field, insightsPayload);
      setActiveInsightsPanel(variables.field);
    },
    onError: (error) => {
      toast.error("Failed to get insights: " + error.message);
    },
  });

  const saveDiscovery = api.studyDesign.saveDiscovery.useMutation({
    onSuccess: () => {
      store.markStepCompleted("operational");
      router.push("/study/new/safety");
    },
    onError: (error) => {
      toast.error("Failed to save: " + error.message);
    },
  });

  const handleGetInsights = async (field: string, forceRefresh = false) => {
    if (!forceRefresh && cachedInsights[field]) {
      setInsightsData(prev => ({
        ...prev,
        [field]: cachedInsights[field]
      }));
      setActiveInsightsPanel(field);
      return;
    }
    
    const context = {
      studyType: store.discovery.studyType || undefined,
      condition: store.discovery.condition || undefined,
      phase: store.discovery.phase || undefined,
      targetEnrollment: store.discovery.population?.targetEnrollment || undefined,
      geographicScope: store.discovery.population?.geographicScope || undefined,
    };

    const queries: Record<string, string> = {
      "site-planning": `What are typical site requirements and recruitment rates for ${store.discovery.condition || "clinical"} trials?`,
      "data-monitoring": `What data management and monitoring approaches are used in ${store.discovery.phase || "Phase 2/3"} trials?`,
    };

    await queryInsights.mutateAsync({
      sessionId: store.sessionId!,
      field,
      context,
      query: queries[field] || "",
    });
  };

  const addCountry = () => {
    if (newCountry && newSiteCount) {
      const count = parseInt(newSiteCount);
      if (!isNaN(count) && count > 0) {
        setFormData(prev => ({
          ...prev,
          sitesPerCountry: {
            ...prev.sitesPerCountry,
            [newCountry]: count
          }
        }));
        setNewCountry("");
        setNewSiteCount("");
      }
    }
  };

  const removeCountry = (country: string) => {
    setFormData(prev => {
      const updated = { ...prev.sitesPerCountry };
      delete updated[country];
      return { ...prev, sitesPerCountry: updated };
    });
  };

  const getTotalSites = () => {
    return Object.values(formData.sitesPerCountry).reduce((sum, count) => sum + count, 0);
  };

  const handleContinue = () => {
    if (!store.sessionId) {
      toast.error("Session not found");
      return;
    }

    if (!formData.numberOfSites) {
      toast.error("Please specify the number of sites");
      return;
    }

    store.updateDiscovery({ 
      operational: formData
    });

    saveDiscovery.mutate({
      sessionId: store.sessionId,
      data: {
        operational: formData,
      },
    });
  };

  const handleApplySuggestion = (field: string, suggestion: string) => {
    if (field === "site-planning") {
      const sitesMatch = suggestion.match(/(\d+)[\s-]+(\d+)?\s*sites?/i);
      const recruitmentMatch = suggestion.match(/(\d+)[\s-]+(\d+)?\s*patients?.*per\s*(?:site|month)/i);
      
      if (sitesMatch) {
        const sites = sitesMatch[2] ? `${sitesMatch[1]}-${sitesMatch[2]}` : sitesMatch[1];
        setFormData(prev => ({ ...prev, numberOfSites: sites }));
      }
      if (recruitmentMatch) {
        const rate = recruitmentMatch[2] 
          ? `${recruitmentMatch[1]}-${recruitmentMatch[2]} patients/site/month`
          : `${recruitmentMatch[1]} patients/site/month`;
        setFormData(prev => ({ ...prev, recruitmentRate: rate }));
      }
      
      toast.success("Site planning parameters updated");
    }
  };

  return (
    <div className="space-y-8">
      <BlurFade delay={0.1} inView>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Operational Planning
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Plan site selection, recruitment, and data management
          </p>
        </div>
      </BlurFade>

      <BlurFade delay={0.2} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Site Planning
                </CardTitle>
                <CardDescription>
                  Define site requirements and geographic distribution
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("site-planning")}
                onRefresh={() => handleGetInsights("site-planning", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "site-planning"}
                hasCachedData={!!cachedInsights["site-planning"]}
                showRefresh={!!cachedInsights["site-planning"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="numberOfSites">Total Number of Sites</Label>
                <Input
                  id="numberOfSites"
                  placeholder="e.g., 5, 10-15"
                  value={formData.numberOfSites}
                  onChange={(e) => setFormData({ ...formData, numberOfSites: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="recruitmentRate">Expected Recruitment Rate</Label>
                <Input
                  id="recruitmentRate"
                  placeholder="e.g., 2-3 patients/site/month"
                  value={formData.recruitmentRate}
                  onChange={(e) => setFormData({ ...formData, recruitmentRate: e.target.value })}
                />
              </div>
            </div>

            <div className="space-y-3">
              <Label>Sites by Country/Region</Label>
              {Object.entries(formData.sitesPerCountry).map(([country, count]) => (
                <div key={country} className="flex items-center gap-2 rounded-lg border p-3">
                  <Globe className="h-4 w-4 text-gray-400" />
                  <span className="flex-1 text-sm">{country}</span>
                  <Badge variant="secondary">{count} sites</Badge>
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={() => removeCountry(country)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              
              <div className="flex gap-2">
                <Input
                  placeholder="Country/Region name"
                  value={newCountry}
                  onChange={(e) => setNewCountry(e.target.value)}
                />
                <Input
                  placeholder="# of sites"
                  type="number"
                  className="w-32"
                  value={newSiteCount}
                  onChange={(e) => setNewSiteCount(e.target.value)}
                />
                <Button onClick={addCountry} size="icon">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              
              {getTotalSites() > 0 && (
                <div className="rounded-lg bg-purple-50 dark:bg-purple-900/20 p-3">
                  <p className="text-sm font-medium text-purple-900 dark:text-purple-200">
                    Total: {getTotalSites()} sites across {Object.keys(formData.sitesPerCountry).length} countries
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.3} inView>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Enrollment Projections
            </CardTitle>
            <CardDescription>
              Expected attrition and enrollment rates
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="screenFailureRate">Screen Failure Rate</Label>
                <Input
                  id="screenFailureRate"
                  placeholder="e.g., 20%, 15-25%"
                  value={formData.screenFailureRate}
                  onChange={(e) => setFormData({ ...formData, screenFailureRate: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="dropoutRate">Expected Dropout Rate</Label>
                <Input
                  id="dropoutRate"
                  placeholder="e.g., 15%, 10-20%"
                  value={formData.dropoutRate}
                  onChange={(e) => setFormData({ ...formData, dropoutRate: e.target.value })}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.4} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  Data Management & Monitoring
                </CardTitle>
                <CardDescription>
                  Define data collection and monitoring strategies
                </CardDescription>
              </div>
              <InsightsButton
                onClick={() => handleGetInsights("data-monitoring")}
                onRefresh={() => handleGetInsights("data-monitoring", true)}
                loading={queryInsights.isPending && queryInsights.variables?.field === "data-monitoring"}
                hasCachedData={!!cachedInsights["data-monitoring"]}
                showRefresh={!!cachedInsights["data-monitoring"]}
              />
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="dataManagement">Data Management System</Label>
                <Select
                  value={formData.dataManagement}
                  onValueChange={(value) => setFormData({ ...formData, dataManagement: value as any })}
                >
                  <SelectTrigger id="dataManagement">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="edc">Electronic Data Capture (EDC)</SelectItem>
                    <SelectItem value="paper">Paper CRF</SelectItem>
                    <SelectItem value="hybrid">Hybrid (EDC + Paper)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="monitoringApproach">Monitoring Approach</Label>
                <Select
                  value={formData.monitoringApproach}
                  onValueChange={(value) => setFormData({ ...formData, monitoringApproach: value as any })}
                >
                  <SelectTrigger id="monitoringApproach">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="on-site">100% On-site Monitoring</SelectItem>
                    <SelectItem value="remote">Remote Monitoring</SelectItem>
                    <SelectItem value="risk-based">Risk-Based Monitoring</SelectItem>
                    <SelectItem value="hybrid">Hybrid Monitoring</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.5} inView>
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={() => router.push("/study/new/timeline")}
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <Button
            onClick={handleContinue}
            disabled={saveDiscovery.isPending}
          >
            {saveDiscovery.isPending ? "Saving..." : "Continue to Safety Planning"}
          </Button>
        </div>
      </BlurFade>

      {activeInsightsPanel && (
        <InsightsPanel
          isOpen={true}
          onClose={() => setActiveInsightsPanel(null)}
          title={`Insights: ${activeInsightsPanel.replace("-", " ").replace(/\b\w/g, l => l.toUpperCase())}`}
          description="Recommendations based on similar studies"
          loading={queryInsights.isPending}
          sections={insightsData[activeInsightsPanel]?.sections || cachedInsights[activeInsightsPanel]?.sections || []}
          sources={insightsData[activeInsightsPanel]?.sources || cachedInsights[activeInsightsPanel]?.sources || []}
          onDocumentClick={(url) => setDocumentViewerUrl(url)}
          onApplySuggestion={(suggestion) => handleApplySuggestion(activeInsightsPanel, suggestion)}
        />
      )}

      {documentViewerUrl && (
        <DocumentViewer
          isOpen={true}
          onClose={() => setDocumentViewerUrl(null)}
          documentUrl={documentViewerUrl}
          loading={false}
        />
      )}
    </div>
  );
}