"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { 
  ChevronLeft, 
  FileText, 
  Download,
  Send,
  CheckCircle,
  Edit2,
  Sparkles,
  Copy,
  CheckCircle2,
  FileDown,
  FileJson
} from "lucide-react";

export default function ReviewPage() {
  const router = useRouter();
  const store = useTrialDesignStore();
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedSections, setGeneratedSections] = useState<Record<string, string>>({});
  const [currentSection, setCurrentSection] = useState<string>("");
  const [copied, setCopied] = useState(false);
  
  const discovery = store.discovery;

  const generateSynopsis = api.studyDesign.generateSynopsis.useMutation({
    onSuccess: (data) => {
      // Set all sections at once
      setGeneratedSections(data.sections);
      setIsGenerating(false);
      toast.success("Synopsis generated successfully!");
      // Store in the store for the synopsis page
      store.setSynopsis(data.fullDocument);
    },
    onError: (error) => {
      toast.error("Failed to generate synopsis: " + error.message);
      setIsGenerating(false);
      setCurrentSection("");
    },
  });

  const handleGenerateSynopsis = async () => {
    if (!store.sessionId) {
      toast.error("Session not found");
      return;
    }

    setIsGenerating(true);
    setGeneratedSections({});
    
    // Simulate progressive section generation
    const sections = [
      "Study Overview",
      "Study Design and Methodology",
      "Study Population",
      "Interventions and Treatments",
      "Study Endpoints and Outcomes",
      "Study Procedures and Assessments",
      "Operational and Regulatory Considerations"
    ];
    
    // Show progress through sections
    let sectionIndex = 0;
    const progressInterval = setInterval(() => {
      if (sectionIndex < sections.length) {
        setCurrentSection(sections[sectionIndex]);
        sectionIndex++;
      }
    }, 500);
    
    // Generate all sections
    generateSynopsis.mutate({
      sessionId: store.sessionId,
    });
    
    // Clear interval when done
    setTimeout(() => clearInterval(progressInterval), sections.length * 500);
  };

  const editSection = (section: string) => {
    switch(section) {
      case 'study-type':
        router.push('/study/new');
        break;
      case 'basics':
        router.push('/study/new/basics');
        break;
      case 'study-design':
        router.push('/study/new/study-design');
        break;
      case 'population':
        router.push('/study/new/population');
        break;
      case 'operational':
        router.push('/study/new/operational');
        break;
    }
  };

  const handleCopy = () => {
    const fullSynopsis = store.synopsis || Object.values(generatedSections).join('\n\n');
    navigator.clipboard.writeText(fullSynopsis);
    setCopied(true);
    toast.success("Synopsis copied to clipboard!");
    setTimeout(() => setCopied(false), 2000);
  };

  const handleExport = (format: "md" | "pdf" | "json") => {
    toast.info(`Export as ${format.toUpperCase()} functionality coming soon!`);
    // In production, this would trigger actual export
  };

  const handleComplete = () => {
    // Generate study title
    const intervention = discovery.intervention?.name || "Investigational Drug";
    const condition = discovery.condition || "Target Condition";
    const phase = discovery.phase?.toUpperCase() || "Phase 2";
    const title = `${phase} Study of ${intervention} in ${condition}`;
    
    // Get the full synopsis
    const fullSynopsis = store.synopsis || Object.values(generatedSections).join('\n\n');
    
    // Save the completed study
    store.saveCompletedStudy(title, fullSynopsis);
    store.markStepCompleted("review");
    
    toast.success("Trial design completed! 🎉");
    router.push("/dashboard");
  };

  return (
    <div className="space-y-8">
      <BlurFade delay={0.1} inView>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Review Your Study Design
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Review all details before generating your clinical trial synopsis
          </p>
        </div>
      </BlurFade>

      {/* Study Type & Basics */}
      <BlurFade delay={0.2} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Study Overview</CardTitle>
                <CardDescription>Basic study information</CardDescription>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => editSection('basics')}
              >
                <Edit2 className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Study Type</p>
                <Badge variant="secondary" className="mt-1">
                  {discovery.studyType || "Not specified"}
                </Badge>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Condition</p>
                <p className="text-sm mt-1">{discovery.condition || "Not specified"}</p>
              </div>
              {discovery.intervention.name && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Intervention</p>
                  <p className="text-sm mt-1">{discovery.intervention.name}</p>
                </div>
              )}
              {discovery.intervention.class && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Drug Class</p>
                  <p className="text-sm mt-1">{discovery.intervention.class}</p>
                </div>
              )}
              {discovery.intervention.isNewCompound !== undefined && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Compound Type</p>
                  <Badge variant="outline" className="mt-1">
                    {discovery.intervention.isNewCompound ? "Novel" : "Existing"}
                  </Badge>
                </div>
              )}
            </div>
            {discovery.intervention.mechanism && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Mechanism of Action</p>
                <p className="text-sm mt-1">{discovery.intervention.mechanism}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </BlurFade>

      {/* Study Design */}
      <BlurFade delay={0.3} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Study Design</CardTitle>
                <CardDescription>Phase, design type, and endpoints</CardDescription>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => editSection('study-design')}
              >
                <Edit2 className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Phase</p>
                <Badge className="mt-1">
                  {discovery.phase || "Not specified"}
                </Badge>
              </div>
              {discovery.design?.designType && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Design Type</p>
                  <Badge variant="secondary" className="mt-1">
                    {discovery.design.designType}
                  </Badge>
                </div>
              )}
              {discovery.design?.randomizationRatio && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Randomization</p>
                  <p className="text-sm mt-1">{discovery.design.randomizationRatio}</p>
                </div>
              )}
              {discovery.design?.blinding && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Blinding</p>
                  <Badge variant="outline" className="mt-1">
                    {discovery.design.blinding}
                  </Badge>
                </div>
              )}
              {discovery.design?.controlType && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Control Type</p>
                  <p className="text-sm mt-1">{discovery.design.controlType}</p>
                </div>
              )}
            </div>
            {discovery.objectives?.primaryGoal && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Primary Endpoint</p>
                <p className="text-sm mt-1">{discovery.objectives.primaryGoal}</p>
              </div>
            )}
            {discovery.objectives?.secondaryGoals && discovery.objectives.secondaryGoals.length > 0 && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Secondary Endpoints</p>
                <ul className="mt-1 list-disc list-inside">
                  {discovery.objectives.secondaryGoals.map((goal, idx) => (
                    <li key={idx} className="text-sm">{goal}</li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      </BlurFade>

      {/* Population */}
      <BlurFade delay={0.4} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Population</CardTitle>
                <CardDescription>Target patient demographics</CardDescription>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => editSection('population')}
              >
                <Edit2 className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Age Range</p>
                <p className="text-sm mt-1">
                  {discovery.population.ageMin || 18} - {discovery.population.ageMax || 65} years
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Gender</p>
                <p className="text-sm mt-1 capitalize">{discovery.population.gender}</p>
              </div>
              {discovery.population.targetEnrollment && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Target Enrollment</p>
                  <p className="text-sm mt-1">{discovery.population.targetEnrollment} participants</p>
                </div>
              )}
              {discovery.population.geographicScope && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Geographic Scope</p>
                  <Badge variant="outline" className="mt-1">
                    {discovery.population.geographicScope}
                  </Badge>
                </div>
              )}
            </div>
            {discovery.population.specificPopulation && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Specific Population</p>
                <p className="text-sm mt-1">{discovery.population.specificPopulation}</p>
              </div>
            )}
            {discovery.population.inclusionCriteria && discovery.population.inclusionCriteria.length > 0 && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Key Inclusion Criteria</p>
                <ul className="mt-1 list-disc list-inside">
                  {discovery.population.inclusionCriteria.slice(0, 3).map((criteria, idx) => (
                    <li key={idx} className="text-sm">{criteria}</li>
                  ))}
                  {discovery.population.inclusionCriteria.length > 3 && (
                    <li className="text-sm text-muted-foreground">
                      +{discovery.population.inclusionCriteria.length - 3} more
                    </li>
                  )}
                </ul>
              </div>
            )}
            {discovery.population.exclusionCriteria && discovery.population.exclusionCriteria.length > 0 && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Key Exclusion Criteria</p>
                <ul className="mt-1 list-disc list-inside">
                  {discovery.population.exclusionCriteria.slice(0, 3).map((criteria, idx) => (
                    <li key={idx} className="text-sm">{criteria}</li>
                  ))}
                  {discovery.population.exclusionCriteria.length > 3 && (
                    <li className="text-sm text-muted-foreground">
                      +{discovery.population.exclusionCriteria.length - 3} more
                    </li>
                  )}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      </BlurFade>

      {/* Timeline & Visits */}
      <BlurFade delay={0.5} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Timeline & Visits</CardTitle>
                <CardDescription>Study periods and visit schedule</CardDescription>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/study/new/timeline')}
              >
                <Edit2 className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              {discovery.timeline?.screeningPeriod && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Screening Period</p>
                  <p className="text-sm mt-1">{discovery.timeline.screeningPeriod}</p>
                </div>
              )}
              {discovery.timeline?.treatmentPeriod && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Treatment Period</p>
                  <p className="text-sm mt-1">{discovery.timeline.treatmentPeriod}</p>
                </div>
              )}
              {discovery.timeline?.followUpPeriod && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Follow-up Period</p>
                  <p className="text-sm mt-1">{discovery.timeline.followUpPeriod}</p>
                </div>
              )}
              {discovery.timeline?.totalDuration && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Duration</p>
                  <Badge className="mt-1">
                    {discovery.timeline.totalDuration}
                  </Badge>
                </div>
              )}
            </div>
            {discovery.timeline?.visits && discovery.timeline.visits.length > 0 && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Scheduled Visits</p>
                <div className="mt-2 space-y-1">
                  {discovery.timeline.visits.slice(0, 3).map((visit, idx) => (
                    <div key={idx} className="flex items-center gap-2">
                      <Badge variant={visit.critical ? "default" : "outline"} className="text-xs">
                        {visit.name}
                      </Badge>
                      <span className="text-xs text-muted-foreground">{visit.timepoint}</span>
                    </div>
                  ))}
                  {discovery.timeline.visits.length > 3 && (
                    <p className="text-xs text-muted-foreground">
                      +{discovery.timeline.visits.length - 3} more visits
                    </p>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </BlurFade>

      {/* Operational Planning */}
      <BlurFade delay={0.6} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Operational Planning</CardTitle>
                <CardDescription>Sites, recruitment, and data management</CardDescription>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => editSection('operational')}
              >
                <Edit2 className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              {discovery.operational?.numberOfSites && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Number of Sites</p>
                  <p className="text-sm mt-1">{discovery.operational.numberOfSites}</p>
                </div>
              )}
              {discovery.operational?.recruitmentRate && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Recruitment Rate</p>
                  <p className="text-sm mt-1">{discovery.operational.recruitmentRate}</p>
                </div>
              )}
              {discovery.operational?.screenFailureRate && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Screen Failure Rate</p>
                  <p className="text-sm mt-1">{discovery.operational.screenFailureRate}</p>
                </div>
              )}
              {discovery.operational?.dropoutRate && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Expected Dropout Rate</p>
                  <p className="text-sm mt-1">{discovery.operational.dropoutRate}</p>
                </div>
              )}
              {discovery.operational?.dataManagement && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Data Management</p>
                  <Badge variant="secondary" className="mt-1">
                    {discovery.operational.dataManagement === 'edc' ? 'Electronic Data Capture' : 
                     discovery.operational.dataManagement === 'paper' ? 'Paper CRF' : 'Hybrid'}
                  </Badge>
                </div>
              )}
              {discovery.operational?.monitoringApproach && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Monitoring Approach</p>
                  <Badge variant="outline" className="mt-1">
                    {discovery.operational.monitoringApproach}
                  </Badge>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Summary Stats */}
      <BlurFade delay={0.7} inView>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Completion</p>
                  <p className="text-2xl font-bold">
                    {store.completedSteps.length}/{6}
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Study Type</p>
                  <p className="text-2xl font-bold capitalize">
                    {discovery.studyType || "Not set"}
                  </p>
                </div>
                <FileText className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Phase</p>
                  <p className="text-2xl font-bold uppercase">
                    {discovery.phase || "Not set"}
                  </p>
                </div>
                <Sparkles className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      </BlurFade>

      {/* Actions */}
      <BlurFade delay={0.8} inView>
        <Card className="border-[#5A32FA]/20 bg-gradient-to-br from-purple-50 to-teal-50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold">Ready to Generate Synopsis?</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Our AI will create a comprehensive clinical trial synopsis based on your inputs and similar studies
                </p>
              </div>
              <Button
                size="lg"
                onClick={handleGenerateSynopsis}
                disabled={isGenerating || generateSynopsis.isPending}
                className="ml-4"
              >
                {isGenerating || generateSynopsis.isPending ? (
                  <>
                    <Sparkles className="mr-2 h-5 w-5 animate-pulse" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Send className="mr-2 h-5 w-5" />
                    Generate Synopsis
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      {/* Generated Synopsis Display */}
      {(isGenerating || Object.keys(generatedSections).length > 0) && (
        <BlurFade delay={0.85} inView>
          <Card className="mt-8">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Generated Study Synopsis
                  </CardTitle>
                  {isGenerating && currentSection && (
                    <CardDescription className="flex items-center gap-2 mt-1">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
                      Generating: {currentSection}...
                    </CardDescription>
                  )}
                </div>
                {Object.keys(generatedSections).length === 7 && (
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCopy}
                    >
                      {copied ? (
                        <>
                          <CheckCircle2 className="mr-2 h-4 w-4" />
                          Copied!
                        </>
                      ) : (
                        <>
                          <Copy className="mr-2 h-4 w-4" />
                          Copy
                        </>
                      )}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleExport("md")}
                    >
                      <FileDown className="mr-2 h-4 w-4" />
                      Markdown
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleExport("pdf")}
                    >
                      <Download className="mr-2 h-4 w-4" />
                      PDF
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleExport("json")}
                    >
                      <FileJson className="mr-2 h-4 w-4" />
                      JSON
                    </Button>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div className="prose prose-sm max-w-none space-y-6">
                {Object.entries(generatedSections).map(([key, content]) => (
                  <div key={key} className="border-b pb-4 last:border-0">
                    <div 
                      className="synopsis-section"
                      dangerouslySetInnerHTML={{ 
                        __html: content
                          .replace(/# /g, '<h3 class="text-lg font-bold mb-3">')
                          .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                          .replace(/\n\n/g, '</p><p class="mb-3">')
                          .replace(/^/, '<p class="mb-3">')
                          .replace(/$/, '</p>')
                      }} 
                    />
                  </div>
                ))}
                {isGenerating && Object.keys(generatedSections).length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <div className="mb-4">
                      <div className="h-8 w-8 mx-auto animate-spin rounded-full border-4 border-primary border-t-transparent" />
                    </div>
                    Preparing your study synopsis...
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </BlurFade>
      )}

      <BlurFade delay={0.9} inView>
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={() => router.push("/study/new/operational")}
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div className="flex gap-2">
            {Object.keys(generatedSections).length === 7 && (
              <Button 
                onClick={handleComplete}
                className="bg-gradient-to-r from-[#5A32FA] to-[#7D2AE8]"
              >
                <CheckCircle2 className="mr-2 h-4 w-4" />
                Complete & Save to Dashboard
              </Button>
            )}
          </div>
        </div>
      </BlurFade>
    </div>
  );
}