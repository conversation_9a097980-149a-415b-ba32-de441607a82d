"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { But<PERSON> } from "~/components/ui/button";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { 
  ChevronLeft, 
  FileText, 
  Download,
  Send,
  CheckCircle,
  Edit2,
  Sparkles
} from "lucide-react";

export default function ReviewPage() {
  const router = useRouter();
  const store = useTrialDesignStore();
  const [isGenerating, setIsGenerating] = useState(false);
  
  const discovery = store.discovery;

  const generateSynopsis = api.studyDesign.generateSynopsis.useMutation({
    onSuccess: (data) => {
      toast.success("Synopsis generated successfully!");
      // Navigate to synopsis page or show modal
      router.push("/study/new/synopsis");
    },
    onError: (error) => {
      toast.error("Failed to generate synopsis: " + error.message);
      setIsGenerating(false);
    },
  });

  const handleGenerateSynopsis = () => {
    if (!store.sessionId) {
      toast.error("Session not found");
      return;
    }

    setIsGenerating(true);
    generateSynopsis.mutate({
      sessionId: store.sessionId,
    });
  };

  const editSection = (section: string) => {
    switch(section) {
      case 'study-type':
        router.push('/study/new');
        break;
      case 'basics':
        router.push('/study/new/basics');
        break;
      case 'study-design':
        router.push('/study/new/study-design');
        break;
      case 'population':
        router.push('/study/new/population');
        break;
      case 'operational':
        router.push('/study/new/operational');
        break;
    }
  };

  return (
    <div className="space-y-8">
      <BlurFade delay={0.1} inView>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Review Your Study Design
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Review all details before generating your clinical trial synopsis
          </p>
        </div>
      </BlurFade>

      {/* Study Type & Basics */}
      <BlurFade delay={0.2} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Study Overview</CardTitle>
                <CardDescription>Basic study information</CardDescription>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => editSection('basics')}
              >
                <Edit2 className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Study Type</p>
                <Badge variant="secondary" className="mt-1">
                  {discovery.studyType || "Not specified"}
                </Badge>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Condition</p>
                <p className="text-sm mt-1">{discovery.condition || "Not specified"}</p>
              </div>
              {discovery.intervention.name && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Intervention</p>
                  <p className="text-sm mt-1">{discovery.intervention.name}</p>
                </div>
              )}
              {discovery.intervention.class && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Drug Class</p>
                  <p className="text-sm mt-1">{discovery.intervention.class}</p>
                </div>
              )}
              {discovery.intervention.isNewCompound !== undefined && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Compound Type</p>
                  <Badge variant="outline" className="mt-1">
                    {discovery.intervention.isNewCompound ? "Novel" : "Existing"}
                  </Badge>
                </div>
              )}
            </div>
            {discovery.intervention.mechanism && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Mechanism of Action</p>
                <p className="text-sm mt-1">{discovery.intervention.mechanism}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </BlurFade>

      {/* Study Design */}
      <BlurFade delay={0.3} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Study Design</CardTitle>
                <CardDescription>Phase and endpoints</CardDescription>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => editSection('study-design')}
              >
                <Edit2 className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Phase</p>
                <Badge className="mt-1">
                  {discovery.phase || "Not specified"}
                </Badge>
              </div>
            </div>
            {discovery.objectives?.primaryGoal && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Primary Endpoint</p>
                <p className="text-sm mt-1">{discovery.objectives.primaryGoal}</p>
              </div>
            )}
            {discovery.objectives?.secondaryGoals && discovery.objectives.secondaryGoals.length > 0 && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Secondary Endpoints</p>
                <ul className="mt-1 list-disc list-inside">
                  {discovery.objectives.secondaryGoals.map((goal, idx) => (
                    <li key={idx} className="text-sm">{goal}</li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      </BlurFade>

      {/* Population */}
      <BlurFade delay={0.4} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Population</CardTitle>
                <CardDescription>Target patient demographics</CardDescription>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => editSection('population')}
              >
                <Edit2 className="mr-2 h-4 w-4" />
                Edit
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Age Range</p>
                <p className="text-sm mt-1">
                  {discovery.population.ageMin || 18} - {discovery.population.ageMax || 65} years
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Gender</p>
                <p className="text-sm mt-1 capitalize">{discovery.population.gender}</p>
              </div>
            </div>
            {discovery.population.specificPopulation && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Specific Population</p>
                <p className="text-sm mt-1">{discovery.population.specificPopulation}</p>
              </div>
            )}
            {discovery.population.inclusionCriteria && discovery.population.inclusionCriteria.length > 0 && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Key Inclusion Criteria</p>
                <ul className="mt-1 list-disc list-inside">
                  {discovery.population.inclusionCriteria.slice(0, 3).map((criteria, idx) => (
                    <li key={idx} className="text-sm">{criteria}</li>
                  ))}
                  {discovery.population.inclusionCriteria.length > 3 && (
                    <li className="text-sm text-muted-foreground">
                      +{discovery.population.inclusionCriteria.length - 3} more
                    </li>
                  )}
                </ul>
              </div>
            )}
            {discovery.population.exclusionCriteria && discovery.population.exclusionCriteria.length > 0 && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">Key Exclusion Criteria</p>
                <ul className="mt-1 list-disc list-inside">
                  {discovery.population.exclusionCriteria.slice(0, 3).map((criteria, idx) => (
                    <li key={idx} className="text-sm">{criteria}</li>
                  ))}
                  {discovery.population.exclusionCriteria.length > 3 && (
                    <li className="text-sm text-muted-foreground">
                      +{discovery.population.exclusionCriteria.length - 3} more
                    </li>
                  )}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      </BlurFade>

      {/* Summary Stats */}
      <BlurFade delay={0.5} inView>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Completion</p>
                  <p className="text-2xl font-bold">
                    {store.completedSteps.length}/{6}
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Study Type</p>
                  <p className="text-2xl font-bold capitalize">
                    {discovery.studyType || "Not set"}
                  </p>
                </div>
                <FileText className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Phase</p>
                  <p className="text-2xl font-bold uppercase">
                    {discovery.phase || "Not set"}
                  </p>
                </div>
                <Sparkles className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      </BlurFade>

      {/* Actions */}
      <BlurFade delay={0.6} inView>
        <Card className="border-[#5A32FA]/20 bg-gradient-to-br from-purple-50 to-teal-50">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold">Ready to Generate Synopsis?</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Our AI will create a comprehensive clinical trial synopsis based on your inputs and similar studies
                </p>
              </div>
              <Button
                size="lg"
                onClick={handleGenerateSynopsis}
                disabled={isGenerating || generateSynopsis.isPending}
                className="ml-4"
              >
                {isGenerating || generateSynopsis.isPending ? (
                  <>
                    <Sparkles className="mr-2 h-5 w-5 animate-pulse" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Send className="mr-2 h-5 w-5" />
                    Generate Synopsis
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.7} inView>
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={() => router.push("/study/new/operational")}
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => toast.info("Export functionality coming soon!")}
            >
              <Download className="mr-2 h-4 w-4" />
              Export Draft
            </Button>
          </div>
        </div>
      </BlurFade>
    </div>
  );
}