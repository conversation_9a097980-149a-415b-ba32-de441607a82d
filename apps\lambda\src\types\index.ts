// Core domain types for TriaLynx Insights

// Study Types
export type StudyType = 'drug' | 'device' | 'behavioral' | 'diagnostic' | 'other';
export type StudyPhase = 'phase1' | 'phase2' | 'phase3' | 'phase4' | 'unknown';
export type StudyStatus = 'completed' | 'active' | 'recruiting' | 'terminated' | 'withdrawn' | 'suspended';
export type Gender = 'all' | 'male' | 'female';

// Discovery Phase Types
export interface InterventionDetails {
  name?: string;
  category?: string;
  mechanism?: string;
  class?: string;
  isNewCompound?: boolean;
  deviceClass?: string;
}

export interface PopulationCriteria {
  ageMin?: number;
  ageMax?: number;
  gender: Gender;
  specificPopulation?: string;
  inclusionCriteria?: string[];
  exclusionCriteria?: string[];
}

export interface StudyObjectives {
  primaryGoal: string;
  keyOutcome: string;
  secondaryGoals?: string[];
}

export interface DiscoveryData {
  studyType: StudyType;
  phase?: StudyPhase;
  intervention: InterventionDetails;
  condition: string;
  population: PopulationCriteria;
  objectives: StudyObjectives;
}

// Knowledge Base Types
export interface BedrockQueryRequest {
  query: string;
  filters?: {
    startDate?: string;
    endDate?: string;
    studyType?: StudyType;
    phase?: StudyPhase;
    status?: StudyStatus[];
    minEnrollment?: number;
    maxEnrollment?: number;
  };
  maxResults?: number;
  includeTerminated?: boolean;
}

export interface StudyResult {
  id: string;
  nctId: string;
  title: string;
  description: string;
  studyType: StudyType;
  phase?: StudyPhase;
  status: StudyStatus;
  startDate: string;
  completionDate?: string;
  primaryCompletionDate?: string;
  enrollment: number;
  actualEnrollment?: number;
  
  // Relevance
  relevanceScore: number;
  matchedTerms: string[];
  highlights: string[];
  
  // Study details
  conditions: string[];
  interventions: string[];
  sponsors: string[];
  locations: string[];
  
  // Outcomes
  primaryOutcomeMeasures: OutcomeMeasure[];
  secondaryOutcomeMeasures: OutcomeMeasure[];
  
  // Design
  studyDesign: StudyDesign;
  
  // Results (if available)
  results?: StudyResults;
}

export interface OutcomeMeasure {
  measure: string;
  timeFrame: string;
  description?: string;
}

export interface StudyDesign {
  allocation?: string;
  interventionModel?: string;
  primaryPurpose?: string;
  masking?: string;
  whoMasked?: string[];
  designType?: string;
}

export interface StudyResults {
  participantFlow?: ParticipantFlow;
  baselineCharacteristics?: any;
  outcomeMeasures?: any;
  adverseEvents?: AdverseEvents;
}

export interface ParticipantFlow {
  enrolled: number;
  started: number;
  completed: number;
  withdrawnReasons?: Record<string, number>;
}

export interface AdverseEvents {
  serious: number;
  other: number;
  deaths: number;
}

export interface SourceDocument {
  nctId: string;
  title: string;
  s3Uri: string;
  excerpt: string;
  score?: number;
}

export interface BedrockQueryResponse {
  output?: string;
  results?: StudyResult[];
  sections?: any[];
  sources?: SourceDocument[];
  citations?: any[];
  sessionId?: string;
  metadata?: {
    totalResults?: number;
    queryTime?: number;
    searchTerms?: string[];
    appliedFilters?: Record<string, any>;
    modelUsed?: string;
  };
}