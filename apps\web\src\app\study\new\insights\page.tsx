"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Progress } from "~/components/ui/progress";
import { BlurFade } from "~/components/ui/blur-fade";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { 
  Lightbulb, 
  TrendingUp, 
  AlertTriangle, 
  Users, 
  Calendar,
  BarChart3,
  ChevronRight,
  CheckCircle2,
  XCircle,
  ArrowUpRight,
  ArrowDownRight
} from "lucide-react";
import type { GeneratedInsights } from "~/types/trial-design";
import { ShimmerButton } from "~/components/ui/shimmer-button";

export default function InsightsPage() {
  const router = useRouter();
  const store = useTrialDesignStore();
  const [insights, setInsights] = useState<GeneratedInsights | null>(null);

  const { data, isLoading } = api.studyDesign.getInsights.useQuery(
    { sessionId: store.sessionId! },
    { 
      enabled: !!store.sessionId,
    }
  );

  useEffect(() => {
    if (data) {
      setInsights(data);
    }
  }, [data]);

  const handleContinue = () => {
    store.markStepCompleted("insights");
    store.setCurrentStep(2); // Move to design step
    router.push("/study/new/design");
  };

  const getImpactColor = (impact?: string) => {
    switch (impact) {
      case "positive":
        return "text-green-600";
      case "negative":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  const getImpactIcon = (impact?: string) => {
    switch (impact) {
      case "positive":
        return <ArrowUpRight className="h-4 w-4" />;
      case "negative":
        return <ArrowDownRight className="h-4 w-4" />;
      default:
        return null;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "critical":
        return "bg-red-100 text-red-800";
      case "important":
        return "bg-amber-100 text-amber-800";
      default:
        return "bg-blue-100 text-blue-800";
    }
  };

  if (isLoading || !insights) {
    return (
      <div className="space-y-8">
        <BlurFade delay={0.1} inView>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Generating Insights...
            </h1>
            <p className="mt-2 text-lg text-gray-600">
              Analyzing similar studies to inform your trial design
            </p>
          </div>
        </BlurFade>
        
        <BlurFade delay={0.2} inView>
          <Card>
            <CardContent className="py-12">
              <div className="space-y-4">
                <Progress value={33} className="h-2" />
                <p className="text-center text-sm text-gray-600">
                  Processing {insights?.basedOnStudies.length || 0} studies...
                </p>
              </div>
            </CardContent>
          </Card>
        </BlurFade>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <BlurFade delay={0.1} inView>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            AI-Generated Insights
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Based on analysis of {insights.basedOnStudies.length} similar clinical trials
          </p>
        </div>
      </BlurFade>

      {/* Key Statistics */}
      <BlurFade delay={0.2} inView>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <Users className="h-4 w-4 text-[#5A32FA]" />
                <span className="text-xs text-gray-500">Enrollment</span>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold">{insights.statistics.medianEnrollment}</p>
              <p className="text-xs text-gray-600">
                Range: {insights.statistics.enrollmentRange.min}-{insights.statistics.enrollmentRange.max}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <Calendar className="h-4 w-4 text-[#00C4CC]" />
                <span className="text-xs text-gray-500">Duration</span>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold">{insights.statistics.medianDurationMonths}mo</p>
              <p className="text-xs text-gray-600">
                Range: {insights.statistics.durationRange.min}-{insights.statistics.durationRange.max}mo
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CheckCircle2 className="h-4 w-4 text-green-500" />
                <span className="text-xs text-gray-500">Success Rate</span>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold">{Math.round(insights.statistics.successRate * 100)}%</p>
              <p className="text-xs text-gray-600">Studies meeting endpoints</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <XCircle className="h-4 w-4 text-red-500" />
                <span className="text-xs text-gray-500">Dropout Rate</span>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-2xl font-bold">{Math.round(insights.statistics.dropoutRate.median * 100)}%</p>
              <p className="text-xs text-gray-600">Median participant dropout</p>
            </CardContent>
          </Card>
        </div>
      </BlurFade>

      {/* Success Patterns */}
      <BlurFade delay={0.3} inView>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              Success Patterns
            </CardTitle>
            <CardDescription>
              Common factors in successful trials
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {insights.successPatterns.map((pattern) => (
              <div key={pattern.id} className="border-l-4 border-green-500 pl-4 space-y-2">
                <div className="flex items-start justify-between">
                  <p className="font-medium flex items-center gap-2">
                    {pattern.description}
                    {getImpactIcon(pattern.impact)}
                  </p>
                  <Badge variant="secondary">
                    {pattern.frequency}% of studies
                  </Badge>
                </div>
                <p className="text-sm text-gray-600">
                  Confidence: {Math.round(pattern.confidence * 100)}%
                </p>
              </div>
            ))}
          </CardContent>
        </Card>
      </BlurFade>

      {/* Risk Factors */}
      <BlurFade delay={0.4} inView>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-600" />
              Risk Factors
            </CardTitle>
            <CardDescription>
              Common challenges to address
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {insights.riskFactors.map((risk) => (
              <div key={risk.id} className="border-l-4 border-amber-500 pl-4 space-y-2">
                <div className="flex items-start justify-between">
                  <p className="font-medium">{risk.factor}</p>
                  <Badge 
                    variant={risk.impact === "high" ? "destructive" : "secondary"}
                  >
                    {risk.impact} impact
                  </Badge>
                </div>
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Mitigation:</span> {risk.mitigation}
                </p>
              </div>
            ))}
          </CardContent>
        </Card>
      </BlurFade>

      {/* Recommendations */}
      <BlurFade delay={0.5} inView>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5 text-[#5A32FA]" />
              Key Recommendations
            </CardTitle>
            <CardDescription>
              Evidence-based suggestions for your trial
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {insights.recommendations.map((rec) => (
              <div key={rec.id} className="space-y-2">
                <div className="flex items-start justify-between">
                  <div>
                    <p className="font-medium">{rec.area}</p>
                    <p className="text-sm mt-1">{rec.recommendation}</p>
                  </div>
                  <Badge className={getPriorityColor(rec.priority)}>
                    {rec.priority}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600 italic">{rec.rationale}</p>
                {rec.alternatives.length > 0 && (
                  <div className="text-sm text-gray-500">
                    <span className="font-medium">Alternatives:</span> {rec.alternatives.join(", ")}
                  </div>
                )}
              </div>
            ))}
          </CardContent>
        </Card>
      </BlurFade>

      {/* Key Takeaways */}
      <BlurFade delay={0.6} inView>
        <Card className="border-[#5A32FA]/20 bg-gradient-to-br from-purple-50 to-teal-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Key Takeaways
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {insights.keyTakeaways.map((takeaway, index) => (
                <li key={index} className="flex items-start gap-2">
                  <CheckCircle2 className="h-4 w-4 text-[#5A32FA] mt-0.5 flex-shrink-0" />
                  <span className="text-sm">{takeaway}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.7} inView>
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={() => router.push("/study/new/search")}
          >
            Back to Search
          </Button>
          <ShimmerButton onClick={handleContinue}>
            Proceed to Design
            <ChevronRight className="ml-2 h-4 w-4" />
          </ShimmerButton>
        </div>
      </BlurFade>
    </div>
  );
}