"use client";

import { usePathname, useRouter } from "next/navigation";
import Link from "next/link";
import { useTrialDesignStore } from "~/store/trial-design";
import { cn } from "~/lib/utils";
import { CheckCircle2, Circle, ChevronLeft, RotateCcw } from "lucide-react";
import { Button } from "~/components/ui/button";
import { toast } from "sonner";

const steps = [
  { id: "study-type", label: "Study Type", path: "/study/new" },
  { id: "basics", label: "Study Basics", path: "/study/new/basics" },
  { id: "study-design", label: "Study Design", path: "/study/new/study-design" },
  { id: "population", label: "Population", path: "/study/new/population" },
  { id: "operational", label: "Operational", path: "/study/new/operational" },
  { id: "review", label: "Review", path: "/study/new/review" },
];

export default function WizardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const router = useRouter();
  const store = useTrialDesignStore();
  const { completedSteps, sessionId } = store;
  
  const currentStepIndex = steps.findIndex(step => step.path === pathname);
  const currentStep = steps[currentStepIndex];
  
  const handleClearSession = () => {
    store.resetSession();
    if (typeof window !== "undefined") {
      localStorage.removeItem("trial-design-storage");
    }
    toast.success("Session cleared. Starting fresh!");
    router.push("/study/new");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Header */}
      <header className="border-b bg-white shadow-sm">
        <nav className="mx-auto flex max-w-7xl items-center justify-between px-6 py-4">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push("/dashboard")}
              className="gap-2"
            >
              <ChevronLeft className="h-4 w-4" />
              Back to Dashboard
            </Button>
            <div className="h-8 w-px bg-gray-300" />
            <Link href="/" className="flex items-center gap-2">
              <div className="h-8 w-8 rounded-lg gradient-primary" />
              <span className="text-xl font-bold text-gray-900">TriaLynx Insights</span>
            </Link>
          </div>
          <div className="flex items-center gap-4">
            {sessionId && (
              <span className="text-sm text-gray-500">
                Session: {sessionId.slice(-8)}
              </span>
            )}
            <Button 
              variant="outline" 
              size="sm"
              onClick={handleClearSession}
            >
              <RotateCcw className="mr-2 h-4 w-4" />
              Clear Session
            </Button>
          </div>
        </nav>
      </header>

      <div className="mx-auto max-w-7xl px-6 py-8">
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => {
              const isCompleted = completedSteps.includes(step.id);
              const isCurrent = index === currentStepIndex;
              const isPast = index < currentStepIndex;
              
              return (
                <div key={step.id} className="flex flex-1 items-center">
                  <Link
                    href={step.path}
                    className={cn(
                      "flex items-center gap-3 rounded-lg p-3 transition-colors",
                      isCurrent && "bg-purple-50",
                      (isCompleted || isPast) && "cursor-pointer hover:bg-gray-50",
                      !isCompleted && !isPast && !isCurrent && "cursor-not-allowed opacity-50"
                    )}
                    onClick={(e) => {
                      if (!isCompleted && !isPast && !isCurrent) {
                        e.preventDefault();
                      }
                    }}
                  >
                    <div className="flex h-10 w-10 items-center justify-center">
                      {isCompleted || isPast ? (
                        <CheckCircle2 className="h-8 w-8 text-[#5A32FA]" />
                      ) : (
                        <Circle 
                          className={cn(
                            "h-8 w-8",
                            isCurrent ? "text-[#5A32FA]" : "text-gray-400"
                          )} 
                        />
                      )}
                    </div>
                    <div>
                      <p className={cn(
                        "text-sm font-medium",
                        isCurrent ? "text-[#5A32FA]" : "text-gray-900"
                      )}>
                        Step {index + 1}
                      </p>
                      <p className={cn(
                        "text-sm",
                        isCurrent ? "text-[#5A32FA]" : "text-gray-600"
                      )}>
                        {step.label}
                      </p>
                    </div>
                  </Link>
                  
                  {index < steps.length - 1 && (
                    <div className="mx-4 flex-1">
                      <div className={cn(
                        "h-0.5",
                        (isCompleted || isPast) ? "bg-[#5A32FA]" : "bg-gray-200"
                      )} />
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Content */}
        <div className="rounded-xl bg-white p-8 shadow-sm">
          {children}
        </div>
      </div>
    </div>
  );
}