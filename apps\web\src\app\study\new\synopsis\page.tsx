"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { BlurFade } from "~/components/ui/blur-fade";
import { Skeleton } from "~/components/ui/skeleton";
import { 
  FileText,
  Download,
  Copy,
  CheckCircle2,
  FileDown,
  FileJson,
  Sparkles
} from "lucide-react";
import { toast } from "sonner";

export default function SynopsisPage() {
  const router = useRouter();
  const store = useTrialDesignStore();
  const [isGenerating, setIsGenerating] = useState(true);
  const [copied, setCopied] = useState(false);

  // Simulate synopsis generation
  setTimeout(() => setIsGenerating(false), 3000);

  const mockSynopsis = `
# Clinical Trial Synopsis

## Study Title
A Phase 3, Randomized, Double-Blind, Placebo-Controlled Study to Evaluate the Efficacy and Safety of [Intervention] in Patients with [Condition]

## Study Objectives
### Primary Objective
${store.discovery.objectives.primaryGoal || "To evaluate the efficacy of the intervention in the target population"}

### Key Outcome Measure
${store.discovery.objectives.keyOutcome || "Primary efficacy endpoint at 12 weeks"}

## Study Design
- **Type**: ${store.discovery.studyType || "Drug"} trial
- **Phase**: ${store.discovery.phase || "Phase 3"}
- **Design**: Parallel-group, randomized controlled trial
- **Duration**: 18 months total (12 weeks treatment + 6 months follow-up)
- **Sample Size**: 200 participants (based on AI recommendations)

## Study Population
- **Age Range**: ${store.discovery.population.ageMin || 18} - ${store.discovery.population.ageMax || 65} years
- **Gender**: ${store.discovery.population.gender === "all" ? "All genders" : store.discovery.population.gender}
${store.discovery.population.specificPopulation ? `- **Specific Population**: ${store.discovery.population.specificPopulation}` : ""}

## Intervention
- **Name**: ${store.discovery.intervention.name || "Study intervention"}
- **Category**: ${store.discovery.intervention.category || "To be specified"}
- **Mechanism**: ${store.discovery.intervention.mechanism || "To be described"}

## Statistical Considerations
- **Primary Analysis**: Intent-to-treat population
- **Power**: 80% to detect a clinically meaningful difference
- **Significance Level**: α = 0.05 (two-sided)

## Operational Considerations
- **Sites**: 5-8 clinical research centers (recommended)
- **Recruitment Period**: 6 months
- **Total Study Duration**: 18 months

---
*Generated by TriaLynx Insights AI • ${new Date().toLocaleDateString()}*
`;

  const handleCopy = () => {
    navigator.clipboard.writeText(mockSynopsis);
    setCopied(true);
    toast.success("Synopsis copied to clipboard!");
    setTimeout(() => setCopied(false), 2000);
  };

  const handleExport = (format: "md" | "pdf" | "json") => {
    toast.info(`Exporting as ${format.toUpperCase()}...`);
    // In production, this would trigger actual export
  };

  const handleComplete = () => {
    store.markStepCompleted("synopsis");
    store.setCurrentStep(4); // Complete
    toast.success("Trial design completed! 🎉");
    router.push("/dashboard");
  };

  if (isGenerating) {
    return (
      <div className="space-y-8">
        <BlurFade delay={0.1} inView>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Generating Synopsis...
            </h1>
            <p className="mt-2 text-lg text-gray-600">
              Creating your trial protocol synopsis
            </p>
          </div>
        </BlurFade>
        
        <BlurFade delay={0.2} inView>
          <Card>
            <CardContent className="py-12">
              <div className="space-y-4">
                <div className="flex justify-center">
                  <Sparkles className="h-12 w-12 text-[#5A32FA] animate-pulse" />
                </div>
                <div className="space-y-3">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-5/6" />
                </div>
                <p className="text-center text-sm text-gray-600 mt-4">
                  AI is combining your inputs with insights to create a comprehensive synopsis...
                </p>
              </div>
            </CardContent>
          </Card>
        </BlurFade>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <BlurFade delay={0.1} inView>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Trial Synopsis
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            Your AI-generated clinical trial protocol synopsis
          </p>
        </div>
      </BlurFade>

      <BlurFade delay={0.2} inView>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Protocol Synopsis
              </CardTitle>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleCopy}
                >
                  {copied ? (
                    <>
                      <CheckCircle2 className="mr-2 h-4 w-4" />
                      Copied!
                    </>
                  ) : (
                    <>
                      <Copy className="mr-2 h-4 w-4" />
                      Copy
                    </>
                  )}
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleExport("md")}
                >
                  <FileDown className="mr-2 h-4 w-4" />
                  Markdown
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleExport("pdf")}
                >
                  <Download className="mr-2 h-4 w-4" />
                  PDF
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleExport("json")}
                >
                  <FileJson className="mr-2 h-4 w-4" />
                  JSON
                </Button>
              </div>
            </div>
            <CardDescription>
              Review and export your clinical trial synopsis
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="prose prose-sm max-w-none">
              <pre className="whitespace-pre-wrap font-sans text-sm">
                {mockSynopsis}
              </pre>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.3} inView>
        <Card className="border-[#00C4CC]/20 bg-gradient-to-br from-teal-50 to-purple-50">
          <CardHeader>
            <CardTitle className="text-lg">Next Steps</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li className="flex items-start gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span>Review the synopsis with your team</span>
              </li>
              <li className="flex items-start gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span>Refine protocol details in the Design Builder</span>
              </li>
              <li className="flex items-start gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span>Submit for regulatory review</span>
              </li>
              <li className="flex items-start gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span>Begin site selection and recruitment planning</span>
              </li>
            </ul>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.4} inView>
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={() => router.push("/study/new/design")}
          >
            Back to Design
          </Button>
          <Button 
            onClick={handleComplete}
            className="bg-gradient-to-r from-[#5A32FA] to-[#7D2AE8]"
          >
            Complete & Save to Dashboard
          </Button>
        </div>
      </BlurFade>
    </div>
  );
}