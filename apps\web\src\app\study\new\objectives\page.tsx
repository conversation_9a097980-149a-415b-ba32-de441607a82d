"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTrialDesignStore } from "~/store/trial-design";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import { BlurFade } from "~/components/ui/blur-fade";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { api } from "~/trpc/react";
import { toast } from "sonner";
import { ChevronLeft, Target, Plus, X, Sparkles } from "lucide-react";
import { ShimmerButton } from "~/components/ui/shimmer-button";

export default function ObjectivesPage() {
  const router = useRouter();
  const store = useTrialDesignStore();
  
  const [formData, setFormData] = useState({
    primaryGoal: store.discovery.objectives.primaryGoal || "",
    keyOutcome: store.discovery.objectives.keyOutcome || "",
    secondaryGoals: store.discovery.objectives.secondaryGoals || [],
  });

  const [newSecondaryGoal, setNewSecondaryGoal] = useState("");

  const saveDiscovery = api.studyDesign.saveDiscovery.useMutation({
    onSuccess: () => {
      store.markStepCompleted("objectives");
      store.markStepCompleted("discovery");
      store.setCurrentStep(1); // Move to search step
      router.push("/study/new/search");
    },
    onError: (error) => {
      toast.error("Failed to save: " + error.message);
    },
  });

  const handleSubmit = () => {
    if (!formData.primaryGoal) {
      toast.error("Please provide a primary goal");
      return;
    }

    if (!formData.keyOutcome) {
      toast.error("Please provide a key outcome measure");
      return;
    }

    if (!store.sessionId) {
      toast.error("Session not found");
      return;
    }

    // Update store
    store.updateDiscovery({ objectives: formData });

    // Save to backend
    saveDiscovery.mutate({
      sessionId: store.sessionId,
      data: {
        objectives: formData,
      },
    });
  };

  const addSecondaryGoal = () => {
    if (newSecondaryGoal.trim()) {
      setFormData({
        ...formData,
        secondaryGoals: [...formData.secondaryGoals, newSecondaryGoal.trim()],
      });
      setNewSecondaryGoal("");
    }
  };

  const removeSecondaryGoal = (index: number) => {
    setFormData({
      ...formData,
      secondaryGoals: formData.secondaryGoals.filter((_, i) => i !== index),
    });
  };

  // Generate summary for review
  const generateSummary = () => {
    const { studyType, intervention, condition, population } = store.discovery;
    return `We're designing a ${studyType || "clinical"} trial for ${condition || "the specified condition"} using ${intervention.name || "the intervention"}. The study will enroll patients aged ${population.ageMin || 18}-${population.ageMax || 65} (${population.gender === "all" ? "all genders" : population.gender}).`;
  };

  return (
    <div className="space-y-8">
      <BlurFade delay={0.1} inView>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Define study objectives
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            What are you trying to achieve with this trial?
          </p>
        </div>
      </BlurFade>

      <BlurFade delay={0.2} inView>
        <Card className="border-[#5A32FA]/20 bg-gradient-to-br from-purple-50 to-teal-50">
          <CardHeader>
            <CardTitle className="text-sm font-medium text-gray-600">Quick Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-700">{generateSummary()}</p>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.3} inView>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Primary Objective
            </CardTitle>
            <CardDescription>
              The main question your study aims to answer
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="primaryGoal">Primary Goal *</Label>
              <Textarea
                id="primaryGoal"
                placeholder="e.g., To evaluate the efficacy of Drug X in reducing blood pressure in patients with hypertension"
                value={formData.primaryGoal}
                onChange={(e) => setFormData({ ...formData, primaryGoal: e.target.value })}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="keyOutcome">Key Outcome Measure *</Label>
              <Input
                id="keyOutcome"
                placeholder="e.g., Change in systolic blood pressure from baseline at 12 weeks"
                value={formData.keyOutcome}
                onChange={(e) => setFormData({ ...formData, keyOutcome: e.target.value })}
              />
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.4} inView>
        <Card>
          <CardHeader>
            <CardTitle>Secondary Objectives</CardTitle>
            <CardDescription>
              Additional questions or outcomes to explore (optional)
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {formData.secondaryGoals.map((goal, index) => (
              <div key={index} className="flex items-center gap-2 rounded-lg border p-3">
                <span className="flex-1 text-sm">{goal}</span>
                <Button
                  size="icon"
                  variant="ghost"
                  onClick={() => removeSecondaryGoal(index)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
            
            <div className="flex gap-2">
              <Input
                placeholder="Add secondary objective..."
                value={newSecondaryGoal}
                onChange={(e) => setNewSecondaryGoal(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addSecondaryGoal())}
              />
              <Button onClick={addSecondaryGoal} size="icon">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </BlurFade>

      <BlurFade delay={0.5} inView>
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={() => router.push("/study/new/population")}
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <ShimmerButton
            onClick={handleSubmit}
            disabled={!formData.primaryGoal || !formData.keyOutcome || saveDiscovery.isPending}
            className="relative"
          >
            {saveDiscovery.isPending ? (
              "Finding Similar Studies..."
            ) : (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                Find Similar Studies
              </>
            )}
          </ShimmerButton>
        </div>
      </BlurFade>
    </div>
  );
}