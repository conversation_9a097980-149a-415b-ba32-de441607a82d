import { create } from "zustand";
import { persist } from "zustand/middleware";
import type { 
  Discovery<PERSON><PERSON>, 
  StudyResult, 
  GeneratedInsights,
  StudyType,
  StudyPhase,
  Gender
} from "~/types/trial-design";

interface CompletedStudy {
  id: string;
  title: string;
  completedAt: string;
  synopsis: string;
  discovery: TrialDesignState["discovery"];
  phase?: string;
}

interface TrialDesignState {
  // Session Management
  sessionId: string | null;
  currentStep: number;
  completedSteps: string[];
  
  // Discovery Data
  discovery: {
    studyType: StudyType | null;
    phase: StudyPhase | null;
    intervention: {
      name?: string;
      category?: string;
      mechanism?: string;
      class?: string;
      drugClass?: string;
      condition?: string;
      isNewCompound?: boolean;
      deviceClass?: string;
    };
    condition: string;
    population: {
      targetEnrollment?: string;
      ageMin?: number;
      ageMax?: number;
      gender: Gender;
      specificPopulation?: string;
      geographicScope?: "local" | "national" | "international";
      inclusionCriteria: string[];
      exclusionCriteria: string[];
      healthyVolunteers?: boolean;
    };
    objectives: {
      primaryGoal: string;
      keyOutcome: string;
      secondaryGoals?: string[];
      phase?: string;
      primaryEndpoint?: string;
      secondaryEndpoints?: string[];
      studyDuration?: string;
      followUpPeriod?: string;
    };
    design?: {
      designType?: "parallel" | "crossover" | "factorial" | "sequential" | "adaptive" | "cluster" | "stepped-wedge" | "n-of-1" | "single-arm" | "basket" | "umbrella" | "platform";
      randomizationRatio?: string;
      blinding?: "open-label" | "single-blind" | "single-blind-investigator" | "single-blind-assessor" | "double-blind" | "triple-blind" | "quadruple-blind";
      controlType?: "placebo" | "active-comparator" | "standard-of-care" | "dose-comparison" | "sham" | "historical" | "waitlist" | "usual-care" | "no-treatment" | "none";
    };
    timeline?: {
      screeningPeriod?: string;
      baselinePeriod?: string;
      treatmentPeriod?: string;
      followUpPeriod?: string;
      totalDuration?: string;
      visits?: Array<{
        name: string;
        timepoint: string;
        procedures: string[];
        critical?: boolean;
      }>;
    };
    operational?: {
      numberOfSites?: string;
      sitesPerCountry?: Record<string, number>;
      recruitmentRate?: string;
      screenFailureRate?: string;
      dropoutRate?: string;
      dataManagement?: "edc" | "paper" | "hybrid";
      monitoringApproach?: "on-site" | "remote" | "risk-based" | "hybrid";
    };
    safety?: {
      primarySafetyEndpoint?: string;
      safetyRunIn?: boolean;
      dsmb?: boolean;
      stoppingRules?: string[];
      adverseEventGrading?: "ctcae" | "who" | "custom";
      safetyCodingDictionary?: "meddra" | "whoart" | "custom";
    };
    statistical?: {
      primaryAnalysis?: "itt" | "per-protocol" | "modified-itt" | "as-treated";
      sampleSize?: number;
      power?: number;
      alpha?: number;
      interimAnalyses?: number;
      multipleTesting?: "bonferroni" | "holm" | "hochberg" | "none";
      missingDataStrategy?: "locf" | "bocf" | "multiple-imputation" | "mixed-model";
    };
  };
  
  // Knowledge Base Results
  similarStudies: StudyResult[];
  selectedStudies: string[];
  isLoadingStudies: boolean;
  
  // Insights
  insights: GeneratedInsights | null;
  isGeneratingInsights: boolean;
  
  // Synopsis
  synopsis: string | null;
  
  // Completed Studies
  completedStudies: CompletedStudy[];
  
  // Insights Cache
  insightsCache: Record<string, any>;
  documentCache: Record<string, any>;
  
  // Actions
  setSessionId: (id: string) => void;
  setCurrentStep: (step: number) => void;
  markStepCompleted: (step: string) => void;
  updateDiscovery: (data: Partial<TrialDesignState["discovery"]>) => void;
  setSimilarStudies: (studies: StudyResult[]) => void;
  toggleStudySelection: (studyId: string) => void;
  setInsights: (insights: GeneratedInsights) => void;
  setSynopsis: (synopsis: string) => void;
  saveCompletedStudy: (title: string, synopsis: string) => void;
  cacheInsights: (key: string, data: any) => void;
  cacheDocument: (url: string, data: any) => void;
  resetStore: () => void;
  resetSession: () => void;
}

const initialState = {
  sessionId: null,
  currentStep: 0,
  completedSteps: [],
  discovery: {
    studyType: null,
    phase: null,
    intervention: {},
    condition: "",
    population: {
      gender: "all" as const,
      inclusionCriteria: [],
      exclusionCriteria: [],
    },
    objectives: {
      primaryGoal: "",
      keyOutcome: "",
    },
  },
  similarStudies: [],
  selectedStudies: [],
  isLoadingStudies: false,
  insights: null,
  isGeneratingInsights: false,
  synopsis: null,
  completedStudies: [],
  insightsCache: {},
  documentCache: {},
};

export const useTrialDesignStore = create<TrialDesignState>()(
  persist(
    (set) => ({
      ...initialState,
      
      setSessionId: (id) => set({ sessionId: id }),
      
      setCurrentStep: (step) => set({ currentStep: step }),
      
      markStepCompleted: (step) =>
        set((state) => ({
          completedSteps: [...new Set([...state.completedSteps, step])],
        })),
      
      updateDiscovery: (data) =>
        set((state) => ({
          discovery: {
            ...state.discovery,
            ...data,
          },
        })),
      
      setSimilarStudies: (studies) =>
        set({
          similarStudies: studies,
          isLoadingStudies: false,
        }),
      
      toggleStudySelection: (studyId) =>
        set((state) => ({
          selectedStudies: state.selectedStudies.includes(studyId)
            ? state.selectedStudies.filter((id) => id !== studyId)
            : [...state.selectedStudies, studyId],
        })),
      
      setInsights: (insights) =>
        set({
          insights,
          isGeneratingInsights: false,
        }),
      
      setSynopsis: (synopsis) => set({ synopsis }),
      
      saveCompletedStudy: (title, synopsis) =>
        set((state) => ({
          completedStudies: [
            ...state.completedStudies,
            {
              id: `study-${Date.now()}`,
              title,
              completedAt: new Date().toISOString(),
              synopsis,
              discovery: state.discovery,
              phase: state.discovery.phase,
            },
          ],
        })),
      
      cacheInsights: (key, data) =>
        set((state) => ({
          insightsCache: {
            ...state.insightsCache,
            [key]: data,
          },
        })),
      
      cacheDocument: (url, data) =>
        set((state) => ({
          documentCache: {
            ...state.documentCache,
            [url]: data,
          },
        })),
      
      resetStore: () => set(initialState),
      
      resetSession: () => set({
        sessionId: null,
        currentStep: 0,
        completedSteps: [],
        selectedStudies: [],
        insights: null,
      }),
    }),
    {
      name: "trial-design-storage",
      partialize: (state) => ({
        sessionId: state.sessionId,
        currentStep: state.currentStep,
        completedSteps: state.completedSteps,
        discovery: state.discovery,
        selectedStudies: state.selectedStudies,
        synopsis: state.synopsis,
        completedStudies: state.completedStudies,
      }),
    }
  )
);